# Camera Functionality Usage Guide

## Library Added: react-native-image-picker v8.2.1

### Features Implemented:
- ✅ Camera access for taking photos
- ✅ Photo gallery access for selecting existing images
- ✅ Automatic saving to photo gallery when taking photos
- ✅ Cross-platform compatibility (iOS & Android)
- ✅ Proper permissions configured

### How to Use:
1. Tap the "Camera" button (blue button below VGuard buttons)
2. Choose from the popup:
   - **Camera**: Take a new photo (automatically saves to gallery)
   - **Gallery**: Select an existing photo from gallery
   - **Cancel**: Close the dialog

### Permissions Configured:

#### iOS (Info.plist):
- `NSCameraUsageDescription`: Camera access for taking photos
- `NSPhotoLibraryUsageDescription`: Photo library access for selecting photos
- `NSPhotoLibraryAddUsageDescription`: Permission to save photos to gallery

#### Android (AndroidManifest.xml):
- `android.permission.CAMERA`: Camera access
- `android.permission.WRITE_EXTERNAL_STORAGE`: Save photos to storage
- `android.permission.READ_EXTERNAL_STORAGE`: Read photos from storage
- `android.permission.READ_MEDIA_IMAGES`: Read media images (Android 13+)

### Code Implementation:
The camera functionality is implemented in `App.tsx` with:
- `openCamera()`: Shows selection dialog
- `launchCameraFunction()`: Handles camera capture
- `launchGalleryFunction()`: Handles gallery selection
- Proper error handling and user feedback
- Logs all actions for debugging

### Build Status:
✅ **iOS Build**: Successfully compiled and installed on simulator
🔄 **Android Build**: Ready for testing

### Next Steps:
1. Test the camera functionality on iOS simulator/device
2. Test build and archive IPA file
3. If everything works, commit the changes
4. Ready for next library installation
