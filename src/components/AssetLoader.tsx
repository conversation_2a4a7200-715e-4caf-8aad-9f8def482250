import React from 'react';
import { View, Text } from 'react-native';

// This component ensures all assets are referenced and won't be stripped during build
const AssetLoader: React.FC = () => {
  // Reference all large assets to prevent them from being stripped
  const assetReferences = [
    // Large images
    require('../../assets/images/large_image_1.jpg'),
    require('../../assets/images/large_image_2.jpg'),
    require('../../assets/images/large_image_3.jpg'),
    require('../../assets/images/large_image_4.jpg'),
    require('../../assets/images/large_image_5.jpg'),
    require('../../assets/images/large_image_6.jpg'),
    require('../../assets/images/large_image_7.jpg'),
    require('../../assets/images/large_image_8.jpg'),
    require('../../assets/images/large_image_9.jpg'),
    require('../../assets/images/large_image_10.jpg'),
    require('../../assets/images/large_image_11.jpg'),
    require('../../assets/images/large_image_12.jpg'),
    require('../../assets/images/large_image_13.jpg'),
    require('../../assets/images/large_image_14.jpg'),
    require('../../assets/images/large_image_15.jpg'),
    require('../../assets/images/large_image_16.jpg'),
    require('../../assets/images/large_image_17.jpg'),
    require('../../assets/images/large_image_18.jpg'),
    require('../../assets/images/large_image_19.jpg'),
    require('../../assets/images/large_image_20.jpg'),
    
    // Large videos
    require('../../assets/videos/large_video_1.mp4'),
    require('../../assets/videos/large_video_2.mp4'),
    require('../../assets/videos/large_video_3.mp4'),
    require('../../assets/videos/large_video_4.mp4'),
    require('../../assets/videos/large_video_5.mp4'),
    require('../../assets/videos/large_video_6.mp4'),
    require('../../assets/videos/large_video_7.mp4'),
    require('../../assets/videos/large_video_8.mp4'),
    require('../../assets/videos/large_video_9.mp4'),
    require('../../assets/videos/large_video_10.mp4'),
    
    // Large audio
    require('../../assets/audio/large_audio_1.wav'),
    
    // Animation
    require('../../assets/animations/sample_animation.json'),
    
    // Documents
    require('../../assets/documents/comprehensive_documentation.md'),
  ];

  // Reference all data files (these are binary files that need special handling)
  const dataFiles = [
    '../../assets/data/large_data_1.bin',
    '../../assets/data/large_data_2.bin',
    '../../assets/data/large_data_3.bin',
    '../../assets/data/large_data_4.bin',
    '../../assets/data/large_data_5.bin',
    '../../assets/data/large_data_6.bin',
    '../../assets/data/large_data_7.bin',
    '../../assets/data/large_data_8.bin',
    '../../assets/data/large_data_9.bin',
    '../../assets/data/large_data_10.bin',
    '../../assets/data/large_data_11.bin',
    '../../assets/data/large_data_12.bin',
    '../../assets/data/large_data_13.bin',
    '../../assets/data/large_data_14.bin',
    '../../assets/data/large_data_15.bin',
    '../../assets/data/large_data_16.bin',
    '../../assets/data/large_data_17.bin',
    '../../assets/data/large_data_18.bin',
    '../../assets/data/large_data_19.bin',
    '../../assets/data/large_data_20.bin',
    '../../assets/data/large_data_21.bin',
    '../../assets/data/large_data_22.bin',
    '../../assets/data/large_data_23.bin',
    '../../assets/data/large_data_24.bin',
    '../../assets/data/large_data_25.bin',
    '../../assets/data/large_data_26.bin',
    '../../assets/data/large_data_27.bin',
    '../../assets/data/large_data_28.bin',
    '../../assets/data/large_data_29.bin',
    '../../assets/data/large_data_30.bin',
    '../../assets/data/large_data_31.bin',
    '../../assets/data/large_data_32.bin',
    '../../assets/data/large_data_33.bin',
    '../../assets/data/large_data_34.bin',
    '../../assets/data/large_data_35.bin',
    '../../assets/data/large_data_36.bin',
    '../../assets/data/large_data_37.bin',
    '../../assets/data/large_data_38.bin',
    '../../assets/data/large_data_39.bin',
    '../../assets/data/large_data_40.bin',
    '../../assets/data/large_data_41.bin',
    '../../assets/data/large_data_42.bin',
    '../../assets/data/large_data_43.bin',
    '../../assets/data/large_data_44.bin',
    '../../assets/data/large_data_45.bin',
    '../../assets/data/large_data_46.bin',
    '../../assets/data/large_data_47.bin',
    '../../assets/data/large_data_48.bin',
    '../../assets/data/large_data_49.bin',
    '../../assets/data/large_data_50.bin',
    '../../assets/data/large_database.db',
    '../../assets/data/large_json_data.json',
  ];

  const paddingFiles = [
    '../../assets/extra_data/padding_1.bin',
    '../../assets/extra_data/padding_2.bin',
    '../../assets/extra_data/padding_3.bin',
    '../../assets/extra_data/padding_4.bin',
    '../../assets/extra_data/padding_5.bin',
    '../../assets/extra_data/padding_6.bin',
    '../../assets/extra_data/padding_7.bin',
    '../../assets/extra_data/padding_8.bin',
    '../../assets/extra_data/padding_9.bin',
    '../../assets/extra_data/padding_10.bin',
  ];

  return (
    <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10 }}>
        Asset Loader Component
      </Text>
      <Text style={{ fontSize: 14, color: '#666' }}>
        This component ensures all {assetReferences.length} media assets, {dataFiles.length} data files, 
        and {paddingFiles.length} padding files are included in the app bundle.
      </Text>
      <Text style={{ fontSize: 12, color: '#999', marginTop: 10 }}>
        Total assets referenced: {assetReferences.length + dataFiles.length + paddingFiles.length}
      </Text>
    </View>
  );
};

export default AssetLoader;
