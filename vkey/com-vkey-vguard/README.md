REACT-NATIVE VGuard Plugin
==============================

Introduction
------------
V-OS App Protection is an app protection solution built on the V-OS virtual secure element. The V-OS App Protection solution is able to secure the host app against advanced persistent threats, trojans, and rootkits. It offers threat intelligence capability which ensures the integrity of the device, file system, app, keyboard, and network. It also supports real-time threats detection and over-the-air (OTA) update of security policy and firmware to ensure the protection of the app.

Integration
------------
1. Get the assets which contains `signature, firmware, vkeylicensepack, and voscodedesign.vky`.
2. Get the latest version of SDK `(vos-app-protection-android-XXXX-Debug.aar , vos- processor-android-XXXX-Debug.aar)`.
3. Download the profile from `V-OS App Protection Server` dashboard.
4. Get the React Native app protection plugin ( com.vkey.vguard ).
```
Note: If you are using Threat Intelligence, make sure you have these assets below:
`manifest.json`
`sig_cfs`
```
5. Get the latest version of cuckoo filter jar file `( cuckoofilter4j-XXX )`.
```
Note: If you are using TLA, make sure you have the asset below: `tla_enc.cer`
```
6. Make sure that you have the java keystore for development/debugging.
7. Add the following lines to build.gradle file in the app folder:
```javascript
buildTypes {
  release {
    minifyEnabled true
    proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    signingConfig signingConfigs.release
    crunchPngs false
  }
  debug {
    signingConfig signingConfigs.release
  }
}
```

Installation
------------

## Getting started
### Android
Open up package.json. Add line
```javascript
"dependencies": { 
    "react-native-vguard": "file: PATH/com-vkey-vguard",
    ....
}
```

`$ npm install PATH/com-vkey-vguard --save`

### iOS
1. Copy V-Key frameworks to <path to react-native-vguard>/ios

2. Go to React Native App Project root folder
yarn add <relative path to react-native-vguard>
cd ios
pod install

## Manual installation

### Importing assets
Copy the `firmware, signature, vkeylicensepack, voscodesign.vky, and profile` asset files, provided in the package, into the `android/app/src/main/assets/` folder of your app project.
```
Note: Please copy TLA ( tla_enc.cer ) and TI ( manifest.json , sig_cfs ) related
assets if you are using those features.
```

### Android

1. Open project by Android Studio
- Copy `vos-app-protection-android-xxxx.aar` file to `vosAppProtection` and `vos-processor-android-xxxx.aar` to `vosProcessor` inside the project `:react-native-vguard`

2. Open `settings.gradle` file from your application.
- Add two lines to the file.
```javascript
includeBuild("../vkey/com-vkey-vguard/android/vosAppProtection")
includeBuild("../vkey/com-vkey-vguard/android/vosProcessor")
```

3. Insert the following lines inside the dependencies block in `android/app/build.gradle`:
- in dependencies {...} 
```
implementation project(':react-native-vguard')
implementation files('libs/cuckoofilter4j-xxx.jar')
```

4. Open up `android/app/src/main/java/[...]/MainActivity.java`
- Add `import java.lang.ref.WeakReference;` to the imports at the top of the file.
- Add `VGuardPlugin.setCurrentActivity(this);` to `onCreate` method.

5. Open up `android/app/src/main/java/[...]/MainApplication.java`
- Add `import com.vkey.vguard.VGuardPlugin;` to the imports at the top of the file.
- Add `VGuardPlugin.setVGActivityLifecycleCallbacks(this);` into `onCreate` method.

6. Configure permissions and vkey configs, open `android\src\app\AndroidManifest.xml` file
- Add permissions:
```javascript
<uses-permission android:name="android.permission.READ_PHONE_STATE"/>
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.GET_TASKS" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
<uses-permission android:name="android.permission.WAKE_LOCK" />

<queries>
	<intent>
		<action android:name="android.intent.action.MAIN" />
	</intent>
</queries>
```

- Add this configurations to the `<application>` tag:
```javascript
<application
  android:name=".MainApplication"
  android:label="@string/app_name"
  android:icon="@mipmap/ic_launcher"
  android:roundIcon="@mipmap/ic_launcher_round"
  android:allowBackup="false"
  android:supportsRtl="true"
  android:requestLegacyExternalStorage="true"
  android:extractNativeLibs="true"
  android:zygotePreloadName="vkey.android.vos.AppZygote"
  android:theme="@style/AppTheme">
```

- Add these line to `<application>` tag:
```javascript
<activity
	android:name="com.vkey.android.support.permission.VGuardPermissionActivity"
	android:theme="@android:style/Theme.Translucent.NoTitleBar"
	tools:replace="android:theme"/>

<service android:name="com.vkey.android.secure.overlay.OverlayService" />

<service
	android:name="vkey.android.vos.MgService"
	android:enabled="true"
	android:process=":vkey"
	android:isolatedProcess="true" />

<service
	android:name="com.vkey.android.internal.vguard.cache.ProcessHttpRequestIntentService"
	android:permission="android.permission.BIND_JOB_SERVICE" />

<meta-data
  android:name="android.max_aspect"
  android:value="2.1" />

<uses-library
  android:name="org.apache.http.legacy"
  android:required="false" />
```


## Supported platforms
-------------------

-	Android
-	iOS

No need aditioanl implementation, everything is handled by VGuardPlugin

### VGuard Integration

```javascript
import { VGuardPlugin } from 'react-native-vguard'

useEffect(() => {
  startVGuard();
    DeviceEventEmitter.addListener(VGuardPlugin.VGUARD_EVENTS, onVguardEvents);
  return (() => { 	
    DeviceEventEmitter.removeAllListeners(VGuardPlugin.VGUARD_EVENTS);
  });
});

const startVGuard = async () => {
  console.log('START VGUARD');
  // Set url of synchronizing the vos logs if enabled 
  const tlaUrl = "https://stg-cloud.v-key.com";
  if (tlaUrl) {
    VGuardPlugin.setLoggerBaseUrl(tlaUrl);
  }
  // Set TI Url if enabled 
  const tiUrl = "https://stg-cloud.v-key.com/";
  if (tiUrl) {
    VGuardPlugin.setThreatIntelligenceServerURL(tiUrl);
  }
  // 0: DEFAULT, 1: HIGHT 
  VGuardPlugin.setMemoryConfiguration(1);
  // Enable the overlay detection
  if(Platform.OS === 'android'){
    VGuardPlugin.setOverlayDetectionEnabled(false);
  }
  // Intialize Vguard 
  VGuardPlugin.setupVGuard();
}

const = onVguardEvents = event => {
  const action = event.action
  console.log(". event.action: " + event.action)

  if(action == VGuardPlugin.VGUARD_PROFILE_LOADED) {
    console.log("PROFILE_LOADED");
  }
  else if(action == VGuardPlugin.ACTION_SCAN_COMPLETE) {
    const threats = event.data
    if(threats != null && threats.length > 0) {
      for (let i=0; i < threats.length; i++) {
        var threatInfo = threats[i]
        console.log("Threat Info: " + threatInfo.ThreatClass + " - " + threatInfo.ThreatName + " - " + threatInfo.ThreatInfo + " - " + threatInfo.ThreatPackageID)
      }
    } else {
      console.log('Scan complete, no threats found!')
    }
  }
  else if (action == VGuardPlugin.VGUARD_PROFILE_LOADED) {
    console.log("RN: Profile loaded"); 
    // Set TI Url if enabled   
    const tiUrl = ""
    if (tiUrl) { 
      VGuardPlugin.setThreatIntelligenceServerURL(tiUrl)   
    } 
  }
  else if(action == VGuardPlugin.VOS_READY) {
    console.log("v-os return code: " + event.data);
  }
  else if(action == VGuardPlugin.VGUARD_ERROR) {
    const errorCode = event.data;
    if(errorCode == "-1039" || errorCode == "20050") {
        console.log("Emulator is detected: " + errorCode);
    } else {
        console.log("Initialize V-Guard is failure caused: " + errorCode);
    }
  }
  else if(action == VGuardPlugin.VGUARD_SSL_ERROR_DETECTED) {
    const data = event.data
    console.log("alertTitle: " + data.alertTitle);
    console.log("alertMessage: " + data.alertMessage);
  }
  // Below events are for Android only
  else if(action == VGuardPlugin.RESET_VOS_STORAGE) {
    console.log("App has been reset VOS storage, kill app and then run again.");
  }
  else if(action == VGuardPlugin.VGUARD_VIRTUAL_SPACE_DETECTED) {
    console.log("app is running on Virtual Space");
  }
  else if(action == VGuardPlugin.VGUARD_OVERLAY_DETECTED_DISABLE) {
    console.log("OVERLAY DETECTED is DISABLE");
  }
  else if(action == VGuardPlugin.VGUARD_OVERLAY_DETECTED) {
    console.log("OVERLAY DETECTED!");
    // this.showAlert("An overlay view was detected!");
  }
  else if(action == VGuardPlugin.VGUARD_SCREEN_SHARING_DETECTED) {
    console.log("VGUARD_SCREEN_SHARING_DETECTED");
    const data = event.data
    if(data != null && data.length > 0) {
      for (let i=0; i < data.length; i++) {
        console.log("Screen Name: " + data[i]);
      }
    }
  }
  else if(action == VGuardPlugin.VGUARD_HANDLE_THREAT_POLICY) {
    const data = event.data
    // console.log("data: " + data);
    console.log("highest_policy: " + data.highest_policy);
    console.log("alertTitle: " + data.alertTitle);
    console.log("alertMessage: " + data.alertMessage);
    console.log("disabledAppExpired: " + data.disabledAppExpired);

    const threats = data.threats
    if(threats != null && threats.length > 0) {
      for (let i=0; i < threats.length; i++) {
        var threatInfo = threats[i]
        console.log("Threat Info: " + threatInfo.ThreatClass + " - " + threatInfo.ThreatName + " - " + threatInfo.ThreatInfo + " - " + threatInfo.ThreatPackageID)
      }
    } else {
      console.log('VGUARD_HANDLE_THREAT_POLICY, no threats found!')
    }
  }  
  else {
    console.log("message: " + event.data);
  }
}
```