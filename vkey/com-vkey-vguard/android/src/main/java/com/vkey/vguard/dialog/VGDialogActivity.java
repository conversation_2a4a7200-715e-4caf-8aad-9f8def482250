package com.vkey.vguard.dialog;

import android.app.Activity;
import android.app.job.JobScheduler;
import android.content.Context;
import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.vkey.android.vguard.LocalBroadcastManager;
import com.vkey.android.vguard.VGuardBroadcastReceiver;

import java.util.Timer;
import java.util.TimerTask;

public class VGDialogActivity extends Activity implements View.OnClickListener {
    public static final int RESPONSE_ALERT_USER = 1;
    public static final int RESPONSE_QUIT_APP = 2;
    public static final int RESPONSE_DISABLE_APP = 3;
    public static final int DENY_APP = 5;
    public static final int DIALOG = 6;

    public static final String TITLE = "title";
    public static final String MESSAGE = "message";
    public static final String BUTTON_TEXT = "mButtonText";
    public static final String MODE = "mode";
    public static final String ACTUAL_MODE = "actualMode";
    public static final String ACTION_CALLBACK_VGDIALOG = "action_callback_vgdialog";
    private String title = "";
    private String message = "";
    private String mButtonText = "Đóng";
    private static final String HARD_CODE_BUTTON_DIALOG = "Đóng";
    private int mode;
    private int actualMode;
    private Timer quitTimer;

    private final int AUTO_QUIT_INTERVAL_DEFAULT = 30000;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setFinishOnTouchOutside(false);

        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            title = bundle.getString(TITLE);
            message = bundle.getString(MESSAGE);
            mButtonText = bundle.getString(BUTTON_TEXT);
            mode = bundle.getInt(MODE);
            actualMode = bundle.getInt(ACTUAL_MODE);
        }

        // Scheduling timer ONLY for Quit Alert
        if (actualMode == RESPONSE_QUIT_APP || actualMode == RESPONSE_DISABLE_APP || actualMode == DENY_APP) {

            this.quitTimer = new Timer("Quit Timer");
            quitTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    Intent intent = new Intent(ACTION_CALLBACK_VGDIALOG);
                    intent.putExtra("autoQuit", true);
                    LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(intent);

                    Intent profileResponseIntent =
                            new Intent(VGuardBroadcastReceiver.PROFILE_THREAT_RESPONSE);
                    profileResponseIntent.putExtra(VGuardBroadcastReceiver.PROFILE_THREAT_RESPONSE,
                            mode);
                    localBroadcast(getApplicationContext(), profileResponseIntent);
                    Handler handler = new Handler(getMainLooper());
                    handler.post(() -> quit());
                }
            }, AUTO_QUIT_INTERVAL_DEFAULT);

        }

        if (!TextUtils.isEmpty(message) && !TextUtils.isEmpty(mButtonText)) {
            LinearLayout parent = new LinearLayout(this);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            parent.setGravity(Gravity.END);
            parent.setPadding(30, 30, 30, 20);
            parent.setLayoutParams(layoutParams);
            parent.setOrientation(LinearLayout.VERTICAL);

            TextView tvAlert = new TextView(this);
            LinearLayout.LayoutParams alertParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.MATCH_PARENT);
            alertParams.gravity = Gravity.START;
            tvAlert.setLayoutParams(alertParams);
            tvAlert.setText(title);
            tvAlert.setTextSize(14);
            tvAlert.setTypeface(tvAlert.getTypeface(), Typeface.BOLD);
            parent.addView(tvAlert);

            TextView tvMessage = new TextView(this);
            LinearLayout.LayoutParams messageParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.MATCH_PARENT);
            messageParams.gravity = Gravity.START;
            tvMessage.setLayoutParams(messageParams);
            tvMessage.setText(message);
            parent.addView(tvMessage);

            Button tvResult = new Button(this);
            LinearLayout.LayoutParams layoutButton = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);

            layoutButton.setMargins(10, 50, 20, 10);
            tvResult.setLayoutParams(layoutButton);
            tvResult.setText(mButtonText);
            tvResult.setTextSize(16);
            tvResult.setTypeface(tvResult.getTypeface(), Typeface.BOLD);
            tvResult.setPadding(20, 20, 20, 20);
            tvResult.setOnClickListener(this);

            parent.addView(tvResult);
            setContentView(parent);
        }

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return false;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onClick(View v) {

        Intent intent = new Intent(ACTION_CALLBACK_VGDIALOG);
        intent.putExtra("autoQuit", false);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);

        Intent profileResponseIntent =
                new Intent(VGuardBroadcastReceiver.PROFILE_THREAT_RESPONSE);
        profileResponseIntent.putExtra(VGuardBroadcastReceiver.PROFILE_THREAT_RESPONSE,
                mode);

        switch (mode) {
            case RESPONSE_ALERT_USER:
                // hack ahead
                // if there are multiple dialogs stacked, we should not broadcast
                // the profile threat response
                if (mButtonText != null
                        && !mButtonText.equalsIgnoreCase(HARD_CODE_BUTTON_DIALOG)) {
                    localBroadcast(this, profileResponseIntent);
                }
                finish();
                break;
            case DIALOG:
            case RESPONSE_QUIT_APP:
            case RESPONSE_DISABLE_APP:
            case DENY_APP:
                // hack ahead
                // if there are multiple dialogs stacked, we should not
                // broadcast the profile threat response
                if (mButtonText != null
                        && !mButtonText.equalsIgnoreCase(HARD_CODE_BUTTON_DIALOG)) {
                    localBroadcast(this, profileResponseIntent);
                }
                if (this.quitTimer != null) {
                    this.quitTimer.cancel();
                }
                quit();
                break;
            default:
                break;
        }
    }

    private void quit() {
        try {
            setResult(Activity.RESULT_CANCELED);
            finishOffActivities(this);
            //clear IntentService queue
            JobScheduler mJobScheduler = (JobScheduler) getApplicationContext().getSystemService(
                    Context.JOB_SCHEDULER_SERVICE);
            mJobScheduler.cancelAll();

            finishAffinity();
        } catch (Exception ignored) {
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (quitTimer != null) {
            quitTimer.cancel();
        }
        this.quitTimer = null;
    }

    private void localBroadcast(Context context, Intent i) {
        LocalBroadcastManager.getInstance(context).sendBroadcast(i);
    }

    private void finishOffActivities(Context context) {
        localBroadcast(context, new Intent(VGuardBroadcastReceiver.ACTION_FINISH));
    }
}
