package com.vkey.vguard;

import android.os.AsyncTask;
import android.util.Patterns;
import android.webkit.URLUtil;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.vkey.android.secure.net.Response;
import com.vkey.android.secure.net.SecureHttpUrlConnection;
import com.vkey.android.vguard.VGException;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;


public class VGSecureHttpUrlConnection extends ReactContextBaseJavaModule {
    private static final String TAG = VGSecureHttpUrlConnection.class.getName();
    private final ReactApplicationContext reactContext;

    public VGSecureHttpUrlConnection(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }

    @Override
    public String getName() {
        return "VGSecureHttpUrlConnection";
    }

    /**     Private Methods ***/

    /***    React Native Methods ****/
    @ReactMethod
    public void get_urlconnection(String hostname, String endpt, String requestAuthorizationHeader, int timeoutInMilliseconds, Promise promise) {
        UrlParameter connection = new UrlParameter(hostname, endpt, requestAuthorizationHeader, timeoutInMilliseconds);
        MutualTlsNetworkTask task = new MutualTlsNetworkTask(connection, promise) {
            @Override
            public Response action_urlconnection(
                    UrlParameter params) throws VGException {
                return SecureHttpUrlConnection.get_urlconnection(params.hostname, params.endpt, reactContext, params.requestAuthorizationHeader, params.timeoutInMilliseconds);
            }
        };
        task.execute();
    }

    @ReactMethod
    public void post_urlconnection(String hostname, String endpt, String contentType, final String data, String requestAuthorizationHeader, int timeoutInMilliseconds, Promise promise) {
        UrlParameter connection = new UrlParameter(hostname, endpt, contentType, data, requestAuthorizationHeader, timeoutInMilliseconds);
        MutualTlsNetworkTask task = new MutualTlsNetworkTask(connection, promise) {
            @Override
            public Response action_urlconnection(UrlParameter params) throws VGException {
                return SecureHttpUrlConnection.post_urlconnection(params.hostname, params.endpt,
                        params.contentType, params.data,
                        reactContext, params.requestAuthorizationHeader, params.timeoutInMilliseconds);
            }
        };
        task.execute();
    }


    @ReactMethod
    public void delete_urlconnection(String hostname, String endpt, String requestAuthorizationHeader, int timeoutInMilliseconds, Promise promise) {
        UrlParameter connection = new UrlParameter(hostname, endpt, requestAuthorizationHeader, timeoutInMilliseconds);
        MutualTlsNetworkTask task = new MutualTlsNetworkTask(connection, promise) {
            @Override
            public Response action_urlconnection(UrlParameter params) throws VGException {
                return SecureHttpUrlConnection.delete_urlconnection(params.hostname, params.endpt,
                        reactContext, params.requestAuthorizationHeader, params.timeoutInMilliseconds);
            }
        };
        task.execute();
    }

    @ReactMethod
    public void head_urlconnection(String hostname, String endpt, String requestAuthorizationHeader, int timeoutInMilliseconds, Promise promise) {
        UrlParameter connection = new UrlParameter(hostname, endpt, requestAuthorizationHeader, timeoutInMilliseconds);
        MutualTlsNetworkTask task = new MutualTlsNetworkTask(connection, promise) {
            @Override
            public Response action_urlconnection(UrlParameter params) throws VGException {
                return SecureHttpUrlConnection.head_urlconnection(params.hostname, params.endpt,
                        reactContext, params.requestAuthorizationHeader, params.timeoutInMilliseconds);
            }
        };
        task.execute();
    }

    @ReactMethod
    public void options_urlconnection(String hostname, String endpt, String requestAuthorizationHeader, int timeoutInMilliseconds, Promise promise) {
        UrlParameter connection = new UrlParameter(hostname, endpt, requestAuthorizationHeader, timeoutInMilliseconds);
        MutualTlsNetworkTask task = new MutualTlsNetworkTask(connection, promise) {
            @Override
            public Response action_urlconnection(UrlParameter params) throws VGException {
                return SecureHttpUrlConnection.options_urlconnection(params.hostname, params.endpt,
                        reactContext, params.requestAuthorizationHeader, params.timeoutInMilliseconds);
            }
        };
        task.execute();
    }

    @ReactMethod
    public void put_urlconnection(String hostname, String endpt, String contentType, final String data, String requestAuthorizationHeader, int timeoutInMilliseconds, Promise promise) {
        UrlParameter connection = new UrlParameter(hostname, endpt, contentType, data, requestAuthorizationHeader, timeoutInMilliseconds);
        MutualTlsNetworkTask task = new MutualTlsNetworkTask(connection, promise) {
            @Override
            public Response action_urlconnection(UrlParameter params) throws VGException {
                return SecureHttpUrlConnection.put_urlconnection(params.hostname, params.endpt,
                        params.contentType, params.data,
                        reactContext, params.requestAuthorizationHeader, params.timeoutInMilliseconds);
            }
        };
        task.execute();
    }

    @ReactMethod
    public void put_urlconnection_bytesData(String hostname, String endpt, String contentType, String data, String requestAuthorizationHeader, int timeoutInMilliseconds, Promise promise) {
        UrlParameter connection = new UrlParameter(hostname, endpt, contentType, data, requestAuthorizationHeader, timeoutInMilliseconds);
        MutualTlsNetworkTask task = new MutualTlsNetworkTask(connection, promise) {
            @Override
            public Response action_urlconnection(UrlParameter params) throws VGException {
                byte[] bytesData = params.data.getBytes();
                return SecureHttpUrlConnection.put_urlconnection(params.hostname, params.endpt,
                        params.contentType, bytesData,
                        reactContext, params.requestAuthorizationHeader, params.timeoutInMilliseconds);
            }
        };
        task.execute();
    }

    @ReactMethod
    public void trace_urlconnection(String hostname, String endpt, String requestAuthorizationHeader, int timeoutInMilliseconds, Promise promise) {
        UrlParameter connection = new UrlParameter(hostname, endpt, requestAuthorizationHeader, timeoutInMilliseconds);
        MutualTlsNetworkTask task = new MutualTlsNetworkTask(connection, promise) {
            @Override
            public Response action_urlconnection(UrlParameter params) throws VGException {
                return SecureHttpUrlConnection.trace_urlconnection(params.hostname, params.endpt,
                        reactContext, params.requestAuthorizationHeader, params.timeoutInMilliseconds);
            }
        };
        task.execute();
    }


    @ReactMethod
    public void httpGet(final String url, Promise promise) {
        // UrlParameter connection = new UrlParameter(hostname, endpt, requestAuthorizationHeader, timeoutInMilliseconds);
        MutualTlsNetworkTask task = new MutualTlsNetworkTask(null, promise) {
            @Override
            public Response action_urlconnection(UrlParameter params) throws VGException, Exception{
                if (URLUtil.isValidUrl(url) && URLUtil.isHttpsUrl(url)
                        && Patterns.WEB_URL.matcher(url).matches()) {
                    URL obj = new URL(url);
                    HttpURLConnection con = (HttpURLConnection) obj.openConnection();

                    // optional default is GET
                    con.setRequestMethod("GET");

                    int responseCode = con.getResponseCode();

                    BufferedReader in = new BufferedReader(
                            new InputStreamReader(con.getInputStream()));
                    String inputLine;
                    StringBuffer response = new StringBuffer();

                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    in.close();

                    Response _response = new Response();
                    _response.responseCode = responseCode;
                    String data = response.toString();
                    _response.response = data.getBytes();
                    //print result
                    return _response;
                }
                return null;
            }
        };

        task.execute();
    }


    /****   MutualTlsNetworkTask class **/
    private WritableArray headerValues(List<String> values) {
        if(values == null || values.size() == 0) return null;
        WritableArray array = Arguments.createArray();
        for(String value: values) {
            array.pushString(value);
        }
        return array;
    }

    private ReadableMap convertToReadbaleMap(Response response) {
        WritableMap rspMap = Arguments.createMap();
        if (response != null) {
            rspMap.putInt("responseCode", response.responseCode);
            String s = new String(response.response);
            rspMap.putString("response", s);

            Map<String, List<String>> headers = response.headers;
            WritableMap headerMap = Arguments.createMap();
            if(headers != null) {
                for (String key : headers.keySet()) {
                    WritableArray array = headerValues(headers.get(key));
                    if (array != null) {
                        headerMap.putArray(key, array);
                    }
                }
            }
            rspMap.putMap("headers", headerMap);
        }

        return rspMap;
    }

    public abstract class MutualTlsNetworkTask extends AsyncTask<Void, Void, Response> {
        Promise promise;
        UrlParameter params;
        private Exception vgException = null;

        public abstract Response action_urlconnection(UrlParameter params) throws VGException, Exception;

        public MutualTlsNetworkTask(UrlParameter params, Promise promise) {
            this.params = params;
            this.promise = promise;
        }

        @Override
        protected Response doInBackground(Void... voids) {
            Response rsp = null;
            try {
                rsp = action_urlconnection(params);
                if(rsp != null && rsp.response != null) {
                }
            } catch (VGException e) {
                e.printStackTrace();
                vgException = e;
            } catch (Exception e) {
                e.printStackTrace();
                vgException = e;
            }
            return rsp;
        }


        @Override
        protected void onPostExecute(Response result) {
            if (vgException != null) {
                promise.reject(vgException);
            } else {
                ReadableMap readableMap = convertToReadbaleMap(result);
                promise.resolve(readableMap);
            }
        }

    }

    public class UrlParameter {
        String hostname;
        String endpt;
        String requestAuthorizationHeader;
        int timeoutInMilliseconds = 15000;
        String contentType;
        String data;

        public UrlParameter(String hostname, String endpt,
                            String contentType, String data,
                            String requestAuthorizationHeader, int timeoutInMilliseconds) {
            this.hostname = hostname;
            this.endpt = endpt;
            this.contentType = contentType;
            this.data = data;
            this.requestAuthorizationHeader = requestAuthorizationHeader;
            this.timeoutInMilliseconds = timeoutInMilliseconds;
        }

        public UrlParameter(String hostname, String endpt,
                            String requestAuthorizationHeader, int timeoutInMilliseconds) {
            this.hostname = hostname;
            this.endpt = endpt;
            this.requestAuthorizationHeader = requestAuthorizationHeader;
            this.timeoutInMilliseconds = timeoutInMilliseconds;
        }
    }
}
