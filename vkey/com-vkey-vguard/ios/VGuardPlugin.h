//
//  VGuardPlugin.h
//  Copyright (c) 2022 V-Key Pte Ltd. All rights reserved.
//

#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>

#import <VosWrapper/VosWrapper.h>
#import <VGuard/VGuard.h>

@interface VGuardPlugin : RCTEventEmitter <RCTBridgeModule, VGuardManagerProtocol, VGuardExceptionHandlerProtocol, VGuardThreatsDelegate>

// Time measurement for vGuard scan performance
// Declare as a property or static variable if you need to access it across methods
@property (nonatomic, assign) CFAbsoluteTime vGuardScanStartTime;

@end
  


