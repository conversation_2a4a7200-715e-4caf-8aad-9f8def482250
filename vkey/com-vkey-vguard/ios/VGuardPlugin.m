
#import "VGuardPlugin.h"
#import <React/RCTLog.h>
#import "React/RCTBridgeModule.h"

#define VOS_SUCCESS                40200
#define VOS_INIT                   0
#define ERROR                     -1
#define ERROR_mess                @"VGuard is nil"

#define VGUARD_EVENTS             @"VGUARD_EVENTS"
#define VGUARD_ERROR              @"VGUARD_ERROR"
#define VOS_READY                 @"VOS_READY"
#define VGUARD_PROFILE_LOADED     @"VGUARD_PROFILE_LOADED"
#define VGUARD_READY              @"VGUARD_READY"

#define ACTION_FINISH             @"ACTION_FINISH"
#define ACTION_SCAN_COMPLETE      @"ACTION_SCAN_COMPLETE"
#define VOS_READY                 @"VOS_READY"

#define VGUARD_SSL_ERROR_DETECTED @"VGUARD_SSL_ERROR_DETECTED"
#define VGUARD_SCREEN_SHARING_DETECTED @"VGUARD_SCREEN_SHARING_DETECTED"
#define ALERT_TITLE @"Thiết bị của bạn không đảm bảo an toàn"
#define ALERT_CONTENT @"Ứng dụng ACB ONE phát hiện thiết bị hiện tại không an toàn. Để bảo vệ cho tài khoản và thông tin của Quý khách, vui lòng kiểm tra lại thiết bị.\nQuý khách có thể liên hệ Phòng Chăm sóc và Dịch vụ Khách hàng của ACB theo số 1900545486 - (028) 38247247 hoặc ACB gần nhất để được hướng dẫn"
#define QUIT_TITLE @"Ứng dụng sẽ tự đóng sau 30 giây"
#define QUITE_TIME_DEFAULT 30


@interface VGuardPlugin () {
    VGuardManager *vguardMgr;
    NSString *mTiUrl;
    NSString *mTlaUrl;
    NSInteger vosFirmwareCode;
}
@end

@implementation VGuardPlugin
RCT_EXPORT_MODULE();

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}


- (NSDictionary *)constantsToExport
{
    return @{
        VGUARD_EVENTS           : VGUARD_EVENTS,
        VGUARD_READY            : VGUARD_READY,
        VGUARD_SSL_ERROR_DETECTED : VGUARD_SSL_ERROR_DETECTED,
        VGUARD_ERROR            : VGUARD_ERROR,
        VOS_READY               : VOS_READY,
        VGUARD_PROFILE_LOADED   : VGUARD_PROFILE_LOADED,
        ACTION_FINISH           : ACTION_FINISH,
        ACTION_SCAN_COMPLETE    : ACTION_SCAN_COMPLETE,
        VGUARD_SCREEN_SHARING_DETECTED : VGUARD_SCREEN_SHARING_DETECTED
    };
}

+ (BOOL)requiresMainQueueSetup
{
    return YES;
}

- (NSArray<NSString *> *)supportedEvents
{
    return @[VGUARD_EVENTS, VGUARD_READY, VGUARD_ERROR, VOS_READY, VGUARD_SSL_ERROR_DETECTED, ACTION_FINISH, ACTION_SCAN_COMPLETE];
}

-(void)sendEvent:(NSString *)eventName param:(NSObject*)params{
    NSDictionary *emitBody = @{@"action": eventName, @"data": params};
    [self sendEventWithName:VGUARD_EVENTS body:emitBody];
}

- (UIViewController *)topMostViewController {
    UIViewController *topController = [UIApplication sharedApplication].keyWindow.rootViewController;
    
    // Navigate to the top-most presented view controller
    while (topController.presentedViewController) {
        topController = topController.presentedViewController;
    }
    return topController;
}

- (void)showPopupWithTitle:(NSString *)title
                   message:(NSString *)message
                      quit:(BOOL)isQuit
                     error:(NSString *)error
                     after:(NSTimeInterval)time {
    
    if (title == nil || [title isEqualToString:@""]) {
        title = ALERT_TITLE;
    }
    
    if (message == nil || [message isEqualToString:@""]) {
        NSString* vTID = @"N/A";
        if (vguardMgr != nil) {
            vTID = [vguardMgr getTroubleshootingId];
        }
        if (error != nil && ![error isEqualToString:@""]) {
            message = [NSString stringWithFormat:@"%@\nMã lỗi: %@\nTID: %@", ALERT_CONTENT, error, vTID];
        } else {
            message = ALERT_CONTENT;
            message = [NSString stringWithFormat:@"%@\nTID: %@", ALERT_CONTENT, vTID];
        }
    }

    dispatch_async(dispatch_get_main_queue(), ^{
        UIViewController *topViewController = [self topMostViewController];
        
        if (topViewController) {
            UIAlertController *alert = [UIAlertController alertControllerWithTitle:title
                                                                           message:message
                                                                    preferredStyle:UIAlertControllerStyleAlert];

            UIAlertAction *quitAction = [UIAlertAction actionWithTitle:QUIT_TITLE
                                                                 style:UIAlertActionStyleDefault
                                                               handler:^(UIAlertAction *action) {
                // exit(0);
            }];
            [alert addAction:quitAction];
            [topViewController presentViewController:alert animated:YES completion:nil];

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(time * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [alert dismissViewControllerAnimated:YES completion:^{
                    if (isQuit) {
                        // exit(0);
                    }
                }];
            });
        } else {
            NSLog(@"Failed to find top view controller to display alert");
        }
    });
}

- (void)showPopupWithTitle:(NSString *)title message:(NSString *)message quit:(BOOL)isQuit after:(NSTimeInterval)time {
    if (title == nil || [title  isEqual: @""]) {
        title = ALERT_TITLE;
    }
    if (message == nil || [message isEqual:@""]) {
        message = ALERT_CONTENT;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        UIViewController *topViewController = [self topMostViewController];
        
        if (topViewController) {
            UIAlertController *alert = [UIAlertController alertControllerWithTitle:title
                                                                           message:message
                                                                    preferredStyle:UIAlertControllerStyleAlert];
            // add a "Quit" button to the alert
            UIAlertAction *quitAction = [UIAlertAction actionWithTitle:QUIT_TITLE
                                                                 style:UIAlertActionStyleDefault
                                                               handler:^(UIAlertAction *action) {
                // Exit the app when the quit button is tapped
                exit(0);
            }];
            [alert addAction:quitAction];
            [topViewController presentViewController:alert animated:YES completion:nil];
            // Dismiss the alert after the specified time, if needed
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(time * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [alert dismissViewControllerAnimated:YES completion:^{
                    // Exit the app after the alert is dismissed
                    if (isQuit) {
                        exit(0);
                    }
                }];
            });
        } else {
            NSLog(@"Failed to find top view controller to display alert");
        }
    });
}

-(void) configureVguard {
    dispatch_async(dispatch_get_global_queue( DISPATCH_QUEUE_PRIORITY_HIGH, 0), ^(void){
        
        if (self->vguardMgr) {
            [self->vguardMgr start];
        } else {
            // time measurement
            self.vGuardScanStartTime = CFAbsoluteTimeGetCurrent();
//            [self excludeFilesFromIOSBackup];
            self->vosFirmwareCode = VOS_INIT;
            //Background Thread
            VGuardExceptionHandler *exceptionHandler = [VGuardExceptionHandler sharedManager];
            [exceptionHandler setDelegate:self];
            
            if(self->mTlaUrl != nil) {
                NSLog(@"[setupVGuard] VosWrapper.setLoggerBaseUrl: %@", self->mTlaUrl);
                [VosWrapper setLoggerBaseUrl:self->mTlaUrl];
            }
            
            // V-Guard Instance
            self->vguardMgr = [VGuardManager sharedVGuardManager];
            [self->vguardMgr setDelegate:self];
            
            VGuardThreats* vGuardThreats = [VGuardThreats sharedModule];
            [vGuardThreats setDelegate:self];
            
            if(self->mTiUrl != nil) {
                [self->vguardMgr setThreatIntelligenceServerURL:self->mTiUrl];
            }
            
            [self->vguardMgr initializeVGuard];
            [self->vguardMgr start];
        }
    });
}

- (void) triggerRestartVguard {
    [self configureVguard];
}

- (void) excludeFilesFromIOSBackup {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSURL *documentsURL = [[fileManager URLsForDirectory:NSDocumentDirectory inDomains:NSUserDomainMask] firstObject];
    
    NSArray *files = @[@"IRK.enc", @"trust.vos", @"trust.key", @"talk.vos", @"SignerDatastore", @"firmware", @"profile", @"signature", @"vkeylicensepack", @"voscodesign.vky"];
    
    for (NSString *file in files) {
        NSURL *fileURL = [documentsURL URLByAppendingPathComponent:file];
        
        NSError *error = nil;
        BOOL success = [fileURL setResourceValue:@(YES) forKey:NSURLIsExcludedFromBackupKey error:&error];
        
        if (!success) {
            NSLog(@"%@", [NSString stringWithFormat:@"Error excluding %@ from backup %@", [fileURL lastPathComponent], error]);
        } else {
            NSLog(@"%@", [NSString stringWithFormat:@"%@ excluding success!!!", [fileURL lastPathComponent]]);
        }
    }
    NSLog(@"Done excludeFilesFromIOSBackup");
}

#pragma mark - VGuardThreatsDelegate

- (void)vGuardScan:(NSArray*)threatsArray {
    NSLog(@"vGuardScan");
    // Calculate and log vguard performance time
    if (self.vGuardScanStartTime > 0) {
        CFAbsoluteTime endTime = CFAbsoluteTimeGetCurrent();
        CFAbsoluteTime elapsedTime = endTime - self.vGuardScanStartTime;
        double elapsedTimeMs = elapsedTime * 1000; // Convert to milliseconds

        NSString *logMessage = [NSString stringWithFormat:
            @"^vGuard scan completed in %.2fms (%.3fs)",
            elapsedTimeMs,
            elapsedTime];

        NSLog(@"%@", logMessage);

        // Reset the start time for next measurement
        self.vGuardScanStartTime = 0;
    } else {
        NSLog(@"^not case start first time");
    }
    
    NSMutableArray *threats = [[NSMutableArray alloc] init];
    for (NSDictionary* thr in threatsArray) {
        NSString *thClass = thr[@"threatTypeId"];
        NSString *thInfo = thr[@"info"];
        NSString *thName = thr[@"name"];
        NSDictionary *dic = @{@"ThreatClass": thClass, @"ThreatInfo": thInfo, @"ThreatName": thName};
        [threats addObject:dic];
    }
    [self sendEvent:ACTION_SCAN_COMPLETE param:threats];
}

- (void)vGuardDidDetectScreenSharing {
    NSLog(@"vGuardDidDetectScreenSharing");
    [self showPopupWithTitle:@"" message:@"" quit:YES error:@"8000" after:QUITE_TIME_DEFAULT];

    [self sendEvent:VGUARD_SCREEN_SHARING_DETECTED param:@"yes"];
}

- (void)vGuardDidDetectThreats:(NSDictionary *)threatsInfo {
    NSLog(@"vGuardDidDetectThreats");
}

#pragma mark - VGuardManagerProtocol

- (void)vGuardDidDetectSSLError:(NSError *)error {
    [self sendEvent:VGUARD_SSL_ERROR_DETECTED param:error.userInfo];
}

- (void)statusVGuard:(VGUARD_STATUS)status withError:(NSError *)error {
    NSLog(@"VGUARD_READY");
//    [self sendEvent:VGUARD_READY param: [NSNumber numberWithInt:status]];
}

- (void)statusVOS:(VOS_STATUS)status withError:(NSError *)error {
    if (status==VOS_OK) {
        vosFirmwareCode = VOS_SUCCESS; // success
        // [vguardMgr start];
    }
    
    NSNumber *returnCode = [NSNumber numberWithInt:status];
    if(error != nil) {
        vosFirmwareCode = error.code;
        returnCode = [NSNumber numberWithLong:error.code];
        [VosWrapper forceSyncLogs];
        if(error.code == -8 || error.code == -5 || error.code == -3) {
            [vguardMgr resetVOSTrustedStorage];
            NSLog(@"auto trigger resetVOSTrustedStorage");
            self->vguardMgr = nil;
            // delay 1s to avoid trigger so soon when other callback not yet finish
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)),
                           dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
                [self triggerRestartVguard];
            });
            NSLog(@"auto trigger re-start");
        }
        
        // Handler error and quit App in plugin
        if(error.code == -1039 || error.code == 20050 || error.code < -999) {
            NSString *errorCodeString = [NSString stringWithFormat:@"%ld", (long)error.code];
            [self showPopupWithTitle:@"" message:@"" quit:YES error:errorCodeString after:QUITE_TIME_DEFAULT];

        }
    }
    NSLog(@"statusVOS: %@", returnCode);
    
    [self sendEvent:VOS_READY param:returnCode ];
}

- (void)vGuardDidFinishInitializing:(BOOL)status withError:(NSError *)error {
    NSLog(@"vGuardDidFinishInitializing: %@", status ? @"SUCCESS" : @"FAILED");
    NSNumber *returnCode = [NSNumber numberWithInt:status];
    if(error != nil) {
        returnCode = [NSNumber numberWithLong:error.code];
        [VosWrapper forceSyncLogs];
        if (error.code >= 20000 && error.code < 30000 && error.code != 20035) {// ignore 20035 since it was handled in statusVOS callback (-8)
            NSString *errorCodeString = [NSString stringWithFormat:@"%ld", (long)error.code];
            [self showPopupWithTitle:@"" message:@"" quit:YES error:errorCodeString after:QUITE_TIME_DEFAULT];
        } else {
            [self sendEvent:VGUARD_ERROR param: returnCode];
        }
    }
    NSLog(@"vGuardDidFinishInitializing: %@", returnCode);
    [self sendEvent:VGUARD_READY param: returnCode];
}

#pragma mark - VGuardExceptionHandlerProtocol

- (void)vGuardExceptionHandler:(NSException *)exception {
    NSLog(@"vGuardExceptionHandler: %@", exception);
    [self sendEvent:VGUARD_ERROR param: exception.description];
}

- (void)vGuardCrashDetected {
    NSLog(@"vGuardCrashDetected");
}

RCT_EXPORT_METHOD(getIsVosStarted:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int execRet = [VosWrapper execute:nil];
    if (execRet > 0) {
        resolve(@(YES));  // Return true if execRet > 0
    } else {
        resolve(@(NO));   // Return false if execRet < 0
    }
}

RCT_EXPORT_METHOD(resetVOSTrustedStorage) {
    if (vguardMgr != nil) {
        [vguardMgr resetVOSTrustedStorage];
    }
}

RCT_EXPORT_METHOD(destroy) {
    if (vguardMgr != nil) {
        if ([vguardMgr respondsToSelector:@selector(destroy)]) {
            [vguardMgr destroy];
        } else {
            NSLog(@"destroy: API not supported in iOS");
        }
    }
}

RCT_EXPORT_METHOD(requestScan) {
    // Android: - (void)requestScan;
    if (vguardMgr != nil) {
        return [vguardMgr start];
    }
}

RCT_EXPORT_METHOD(getCustomerID:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* cusId = [vguardMgr getCustomerID];
        resolve(cusId);
    } else {
        reject(@"getCustomerID", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(getProcessorVersion:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* processorVer = [VosWrapper getProcessorVersion];
        resolve(processorVer);
    } else {
        reject(@"getProcessorVersion", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(getFirmwareVersion:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* firmwareVer = [VosWrapper getFirmwareVersion];
        resolve(firmwareVer);
    } else {
        reject(@"getFirmwareVersion", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(sdkVersion:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* cusId = [vguardMgr sdkVersion];
        resolve(cusId);
    } else {
        reject(@"sdkVersion", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(getPassword:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* pass = [vguardMgr getPassword];
        resolve(pass);
    } else {
        reject(@"getPassword", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(lockVos:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        int result = [vguardMgr lockVOS];
        resolve([NSNumber numberWithInt:result]);
    } else {
        reject(@"lockVOS", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(encryptUsingCustomerKey:(NSString *)string resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* encryptStr = [vguardMgr encryptString:string];
        resolve(encryptStr);
    } else {
        reject(@"encryptString", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(decryptUsingCustomerKey:(NSString *)string resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* decryptStr = [vguardMgr decryptString:string];
        resolve(decryptStr);
    } else {
        reject(@"decryptString", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(getDFPHashHash:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* dfp = [vguardMgr getDFPHashHash];
        resolve(dfp);
    } else {
        reject(@"getDFPHashHash", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(getVGuardVersionInformation:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* version = [vguardMgr getVGuardVersionInformation];
        resolve(version);
    } else {
        reject(@"getVGuardVersionInformation", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(getTroubleshootingId:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* TID = [vguardMgr getTroubleshootingId];
        resolve(TID);
    } else {
        reject(@"getTroubleshootingId", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(setupVGuard) {
    [self configureVguard];
}

RCT_EXPORT_METHOD(setDebugable:(BOOL)debug) {
    if (vguardMgr != nil) {
        [vguardMgr setIsDebug:debug];
    }
}

RCT_EXPORT_METHOD(setMemoryConfiguration:(nonnull NSNumber *)memConfig) {
    VGuardManager *vgManager = [VGuardManager sharedVGuardManager];
    [vgManager setMemoryConfiguration:[memConfig integerValue]];
}

RCT_EXPORT_METHOD(setMaximumNetworkRetryTime:(nonnull NSNumber *)memConfig) {
    VGuardManager *vgManager = [VGuardManager sharedVGuardManager];
    [vgManager setMaximumNetworkRetryTime:[memConfig intValue]];
}

RCT_EXPORT_METHOD(setThreatIntelligenceServerURL:(NSString *)tiURL) {
    mTiUrl = tiURL;
}

RCT_EXPORT_METHOD(setAllowsArbitraryNetworking:(BOOL)enable) {
    if (vguardMgr != nil) {
        [vguardMgr allowsArbitraryNetworking:enable];
    }
}

RCT_EXPORT_METHOD(setLoggerBaseUrl:(NSString *)tlaURL) {
    mTlaUrl = tlaURL;
}

RCT_EXPORT_METHOD(showPopupVkey:(NSString *)title withContent:(NSString *)content quitApp:(BOOL)isQuitApp quitTime:(nonnull NSNumber *)time) {
    [self showPopupWithTitle:title message:content quit:isQuitApp error:@"" after:[time integerValue]];
}

RCT_EXPORT_METHOD(forceSyncLogs) {
    NSLog(@"VosWrapper.forceSyncLogs");
    [VosWrapper forceSyncLogs];
}

@end
