#import "AppDelegate.h"
#import "VKeyExtension.h"  // Import the VKeyExtension header

#import <React/RCTBundleURLProvider.h>

@interface AppDelegate ()
@property (nonatomic, strong) VKeyExtension *logManager;
@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  self.moduleName = @"TestVersion0753";
  // You can add your custom initial props in the dictionary below.
  // They will be passed down to the ViewController used by React Native.
  self.initialProps = @{};
  
  // Initialize VKeyExtension
  self.logManager = [VKeyExtension sharedManager];
  [self.logManager setupLegacyNotificationSupport];
  
  // Optional: Check and replace VKey assets (iOS 13.4+)
  if (@available(iOS 13.4, *)) {
    // Uncomment the next line if you want to enable asset checking
     [self.logManager checkVKeyAssetsAndReplace];
  }
  
  // Example usage of logging
  [self.logManager logSuccess:@"AppDelegate: Application launched successfully"];
  [self.logManager logInfo:@"AppDelegate: VKeyExtension initialized"];

  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
  return [self bundleURL];
}

- (NSURL *)bundleURL
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

@end
