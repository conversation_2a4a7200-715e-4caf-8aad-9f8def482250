//
//  VKeyExtension.m
//  GeneralCode
//
//  Created by Assistant on 2025-07-09.
//

#import "VKeyExtension.h"
#import <CommonCrypto/CommonCrypto.h>

// Default notification name
static NSString *const kDefaultLogUpdateNotification = @"VKeyExtensionUpdateNotification";

@interface VKeyExtension ()

@property (nonatomic, strong) NSMutableString *plainTextLog;
@property (nonatomic, strong) NSMutableAttributedString *attributedTextLog;
@property (nonatomic, assign) BOOL consoleLoggingEnabled;
@property (nonatomic, strong) NSString *logUpdateNotificationName;

// Private methods
- (UIScrollView *)createButtonScrollView:(UIView *)parentView
                                  target:(id)target
                            buttonTitles:(NSArray<NSString *> *)buttonTitles
                            buttonColors:(NSArray<UIColor *> *)buttonColors
                           buttonActions:(NSArray<NSString *> *)buttonActions
                               topMargin:(CGFloat)topMargin
                             heightRatio:(CGFloat)heightRatio
                            heightOffset:(CGFloat)heightOffset;

- (UIScrollView *)createButtonScrollViewWithSelectors:(UIView *)parentView
                                               target:(id)target
                                         buttonTitles:(NSArray<NSString *> *)buttonTitles
                                         buttonColors:(NSArray<UIColor *> *)buttonColors
                                      buttonSelectors:(NSArray<NSValue *> *)buttonSelectors
                                            topMargin:(CGFloat)topMargin
                                          heightRatio:(CGFloat)heightRatio
                                         heightOffset:(CGFloat)heightOffset;

@end

@implementation VKeyExtension

#pragma mark - Singleton

+ (instancetype)sharedManager {
    static VKeyExtension *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[VKeyExtension alloc] init];
    });
    return sharedInstance;
}

+ (instancetype)shared {
    return [self sharedManager];
}

#pragma mark - Initialization

- (instancetype)init {
    self = [super init];
    if (self) {
        _plainTextLog = [[NSMutableString alloc] init];
        _attributedTextLog = [[NSMutableAttributedString alloc] init];
        _consoleLoggingEnabled = YES;
        _logUpdateNotificationName = kDefaultLogUpdateNotification;
    }
    return self;
}

#pragma mark - Private Helper Methods

- (NSDictionary *)getStyleForLogLevel:(VLogLevel)level {
    switch (level) {
        case VLogLevelSuccess:
            return @{
                @"textColor": [UIColor systemGreenColor],
                @"backgroundColor": [UIColor clearColor],
                @"fontSize": @(14),
                @"isBold": @(YES),
                @"isItalic": @(NO)
            };
            
        case VLogLevelError:
            return @{
                @"textColor": [UIColor systemRedColor],
                @"backgroundColor": [UIColor clearColor],
                @"fontSize": @(14),
                @"isBold": @(YES),
                @"isItalic": @(NO)
            };
            
        case VLogLevelWarning:
            return @{
                @"textColor": [UIColor systemOrangeColor],
                @"backgroundColor": [UIColor clearColor],
                @"fontSize": @(14),
                @"isBold": @(YES),
                @"isItalic": @(NO)
            };
            
        case VLogLevelInfo:
            return @{
                @"textColor": [UIColor systemBlueColor],
                @"backgroundColor": [UIColor clearColor],
                @"fontSize": @(14),
                @"isBold": @(NO),
                @"isItalic": @(YES)
            };
            
        case VLogLevelDebug:
            return @{
                @"textColor": [UIColor systemGrayColor],
                @"backgroundColor": [UIColor clearColor],
                @"fontSize": @(12),
                @"isBold": @(NO),
                @"isItalic": @(NO)
            };
            
        case VLogLevelHighlight:
            return @{
                @"textColor": [UIColor blackColor],
                @"backgroundColor": [UIColor systemYellowColor],
                @"fontSize": @(14),
                @"isBold": @(YES),
                @"isItalic": @(NO)
            };
            
        case VLogLevelDefault:
        default:
            return @{
                @"textColor": [UIColor blackColor],
                @"backgroundColor": [UIColor clearColor],
                @"fontSize": @(14),
                @"isBold": @(NO),
                @"isItalic": @(NO)
            };
    }
}

- (void)postLogUpdateNotificationWithUserInfo:(NSDictionary *)userInfo {
    dispatch_async(dispatch_get_main_queue(), ^{
        [[NSNotificationCenter defaultCenter] postNotificationName:self.logUpdateNotificationName
                                                            object:nil
                                                          userInfo:userInfo];
    });
}

#pragma mark - Public Methods

- (void)logText:(NSString *)logText
      textColor:(UIColor *)textColor
backgroundColor:(UIColor *)backgroundColor
       fontSize:(CGFloat)fontSize
         isBold:(BOOL)isBold
       isItalic:(BOOL)isItalic {
    
    if (!logText) return;
    
    // Set defaults if nil
    if (!textColor) textColor = [UIColor blackColor];
    if (!backgroundColor) backgroundColor = [UIColor clearColor];
    if (fontSize <= 0) fontSize = 14;
    
    // Console logging
    if (self.consoleLoggingEnabled) {
        NSLog(@"^%@", logText);
    }
    
    // Update plain text log
    if (self.plainTextLog.length > 0) {
        [self.plainTextLog appendString:@"\n\n"];
    }
    [self.plainTextLog appendString:logText];
    
    // Create attributed text
    UIFont *font = [self createFontWithSize:fontSize isBold:isBold isItalic:isItalic];
    NSDictionary *attributes = @{
        NSForegroundColorAttributeName: textColor,
        NSBackgroundColorAttributeName: backgroundColor,
        NSFontAttributeName: font
    };

    NSLog(@"DEBUG: Creating attributed text with color: %@", textColor);
    NSAttributedString *newAttributedText = [[NSAttributedString alloc] initWithString:logText attributes:attributes];
    
    // Add line breaks if there's existing content
    if (self.attributedTextLog.length > 0) {
        NSAttributedString *lineBreaks = [[NSAttributedString alloc] initWithString:@"\n\n"];
        [self.attributedTextLog appendAttributedString:lineBreaks];
    }
    
    // Append the new attributed text
    [self.attributedTextLog appendAttributedString:newAttributedText];
    
    // Post notification
    NSDictionary *userInfo = @{
        @"text": [self.plainTextLog copy],
        @"attributedText": newAttributedText,
        @"fullAttributedText": [[NSAttributedString alloc] initWithAttributedString:self.attributedTextLog],
        @"textColor": textColor,
        @"backgroundColor": backgroundColor,
        @"fontSize": @(fontSize),
        @"isBold": @(isBold),
        @"isItalic": @(isItalic)
    };
    
    [self postLogUpdateNotificationWithUserInfo:userInfo];
}

- (void)logText:(NSString *)logText {
    [self logText:logText textColor:nil backgroundColor:nil fontSize:14 isBold:NO isItalic:NO];
}

- (void)logText:(NSString *)logText withLevel:(VLogLevel)level {
    NSDictionary *style = [self getStyleForLogLevel:level];
    
    [self logText:logText
        textColor:style[@"textColor"]
  backgroundColor:style[@"backgroundColor"]
         fontSize:[style[@"fontSize"] floatValue]
           isBold:[style[@"isBold"] boolValue]
         isItalic:[style[@"isItalic"] boolValue]];
}

#pragma mark - Convenience Methods

- (void)logSuccess:(NSString *)logText {
    [self logText:logText withLevel:VLogLevelSuccess];
}

- (void)logError:(NSString *)logText {
    [self logText:logText withLevel:VLogLevelError];
}

- (void)logWarning:(NSString *)logText {
    [self logText:logText withLevel:VLogLevelWarning];
}

- (void)logInfo:(NSString *)logText {
    [self logText:logText withLevel:VLogLevelInfo];
}

- (void)logDebug:(NSString *)logText {
    [self logText:logText withLevel:VLogLevelDebug];
}

- (void)logHighlight:(NSString *)logText {
    [self logText:logText withLevel:VLogLevelHighlight];
}

#pragma mark - Log Management

- (void)clearLogs {
    [self.plainTextLog setString:@""];
    self.attributedTextLog = [[NSMutableAttributedString alloc] init];

    [self logSuccess:@"Logs cleared successfully!"];
}

- (NSString *)getPlainTextLog {
    return [self.plainTextLog copy];
}

- (NSAttributedString *)getAttributedTextLog {
    return [self.attributedTextLog copy];
}

#pragma mark - Legacy AppDelegate Methods (for easy migration)

/**
 * Legacy method that matches AppDelegate's sentLogEvent signature
 * This allows easy migration from AppDelegate to VLogManager
 */
- (void)sentLogEvent:(NSString *)logText
           textColor:(UIColor *)textColor
     backgroundColor:(UIColor *)backgroundColor
            fontSize:(CGFloat)fontSize
              isBold:(BOOL)isBold
            isItalic:(BOOL)isItalic {
    [self logText:logText
        textColor:textColor
  backgroundColor:backgroundColor
         fontSize:fontSize
           isBold:isBold
         isItalic:isItalic];
}

- (void)sentLogEvent:(NSString *)logText {
    [self logText:logText];
}

- (void)sentLogEventSuccess:(NSString *)logText {
    [self logSuccess:logText];
}

- (void)sentLogEventError:(NSString *)logText {
    [self logError:logText];
}

- (void)sentLogEventWarning:(NSString *)logText {
    [self logWarning:logText];
}

- (void)sentLogEventInfo:(NSString *)logText {
    [self logInfo:logText];
}

- (void)sentLogEventDebug:(NSString *)logText {
    [self logDebug:logText];
}

- (void)sentLogEventHighlight:(NSString *)logText {
    [self logHighlight:logText];
}

- (void)clearUILog {
    [self clearLogs];
}

#pragma mark - Swift Compatibility Methods

/**
 * Setup method to configure VLogManager for legacy notification support
 * Call this in AppDelegate to maintain compatibility with existing UI
 */
- (void)setupLegacyNotificationSupport {
    [self setLogUpdateNotificationName:@"textUpdatedNotification"];
}

#pragma mark - Configuration

- (void)setConsoleLoggingEnabled:(BOOL)enabled {
    _consoleLoggingEnabled = enabled;
}

- (void)setLogUpdateNotificationName:(NSString *)notificationName {
    if (notificationName && notificationName.length > 0) {
        _logUpdateNotificationName = notificationName;
    }
}

- (NSString *)getLogUpdateNotificationName {
    return self.logUpdateNotificationName;
}

#pragma mark - UI Management

- (UIFont *)createFontWithSize:(CGFloat)fontSize isBold:(BOOL)isBold isItalic:(BOOL)isItalic {
    UIFont *baseFont;

    if (isBold && isItalic) {
        baseFont = [UIFont boldSystemFontOfSize:fontSize];
        // For italic bold, we need to create a font descriptor
        UIFontDescriptor *descriptor = [baseFont.fontDescriptor fontDescriptorWithSymbolicTraits:UIFontDescriptorTraitBold | UIFontDescriptorTraitItalic];
        if (descriptor) {
            baseFont = [UIFont fontWithDescriptor:descriptor size:fontSize];
        }
    } else if (isBold) {
        baseFont = [UIFont boldSystemFontOfSize:fontSize];
    } else if (isItalic) {
        baseFont = [UIFont italicSystemFontOfSize:fontSize];
    } else {
        baseFont = [UIFont systemFontOfSize:fontSize];
    }

    return baseFont;
}

- (UITextView *)setupLogUIInView:(UIView *)parentView
                      logContent:(NSString *)logContent
                       textColor:(UIColor *)textColor
                 backgroundColor:(UIColor *)backgroundColor
                        fontSize:(CGFloat)fontSize
                          isBold:(BOOL)isBold
                        isItalic:(BOOL)isItalic
                  attributedText:(NSAttributedString *)attributedText {

    if (!parentView) return nil;

    __block UITextView *logView = nil;

    // Execute on main thread synchronously if needed
    if ([NSThread isMainThread]) {
        logView = [[UITextView alloc] init];
        logView.translatesAutoresizingMaskIntoConstraints = NO;
        logView.backgroundColor = [UIColor whiteColor];
        logView.textColor = [UIColor blackColor];
        logView.editable = NO;
        logView.layer.cornerRadius = 5;

        [parentView addSubview:logView];

        // Setup constraints
        [NSLayoutConstraint activateConstraints:@[
            [logView.leadingAnchor constraintEqualToAnchor:parentView.leadingAnchor constant:2],
            [logView.trailingAnchor constraintEqualToAnchor:parentView.trailingAnchor constant:-2],
            [logView.bottomAnchor constraintEqualToAnchor:parentView.bottomAnchor constant:-2],
            [logView.heightAnchor constraintEqualToConstant:parentView.frame.size.height/2]
        ]];

        // Update with initial content
        [self updateLogUI:logView
               logContent:logContent ?: @""
                textColor:textColor
          backgroundColor:backgroundColor
                 fontSize:fontSize
                   isBold:isBold
                 isItalic:isItalic
           attributedText:attributedText];
    } else {
        // If not on main thread, dispatch synchronously to main thread
        dispatch_sync(dispatch_get_main_queue(), ^{
            logView = [[UITextView alloc] init];
            logView.translatesAutoresizingMaskIntoConstraints = NO;
            logView.backgroundColor = [UIColor whiteColor];
            logView.textColor = [UIColor blackColor];
            logView.editable = NO;
            logView.layer.cornerRadius = 5;

            [parentView addSubview:logView];

            // Setup constraints
            [NSLayoutConstraint activateConstraints:@[
                [logView.leadingAnchor constraintEqualToAnchor:parentView.leadingAnchor constant:2],
                [logView.trailingAnchor constraintEqualToAnchor:parentView.trailingAnchor constant:-2],
                [logView.bottomAnchor constraintEqualToAnchor:parentView.bottomAnchor constant:-2],
                [logView.heightAnchor constraintEqualToConstant:parentView.frame.size.height/2]
            ]];

            // Update with initial content
            [self updateLogUI:logView
                   logContent:logContent ?: @""
                    textColor:textColor
              backgroundColor:backgroundColor
                     fontSize:fontSize
                       isBold:isBold
                     isItalic:isItalic
               attributedText:attributedText];
        });
    }

    return logView;
}

- (UITextView *)setupLogUIInView:(UIView *)parentView {
    return [self setupLogUIInView:parentView
                       logContent:@""
                        textColor:[UIColor blackColor]
                  backgroundColor:[UIColor clearColor]
                         fontSize:14
                           isBold:NO
                         isItalic:NO
                   attributedText:nil];
}

- (void)updateLogUI:(UITextView *)logView
         logContent:(NSString *)logContent
          textColor:(UIColor *)textColor
    backgroundColor:(UIColor *)backgroundColor
           fontSize:(CGFloat)fontSize
             isBold:(BOOL)isBold
           isItalic:(BOOL)isItalic
     attributedText:(NSAttributedString *)attributedText {

    if (!logView) return;

    // Set defaults if nil
    if (!textColor) textColor = [UIColor blackColor];
    if (!backgroundColor) backgroundColor = [UIColor clearColor];
    if (fontSize <= 0) fontSize = 14;
    if (!logContent) logContent = @"";

    dispatch_async(dispatch_get_main_queue(), ^{
        // Update the text with formatting - preserve old content
        if (logContent.length == 0) {
            // Show the full accumulated attributed text from VLogManager
            NSAttributedString *attributedLog = [self getAttributedTextLog];
            if (attributedLog.length > 0) {
                logView.attributedText = attributedLog;
            } else {
                logView.text = [self getPlainTextLog];
            }
        } else {
            // Get existing attributed text or create from plain text
            NSAttributedString *existingAttributedText = logView.attributedText ?: [[NSAttributedString alloc] initWithString:logView.text ?: @""];

            // Create new attributed text for the new content
            NSAttributedString *newAttributedText;
            if (attributedText) {
                // Use provided attributed text
                newAttributedText = attributedText;
            } else {
                // Create attributed text with specified formatting
                NSDictionary *attributes = @{
                    NSForegroundColorAttributeName: textColor,
                    NSBackgroundColorAttributeName: backgroundColor,
                    NSFontAttributeName: [self createFontWithSize:fontSize isBold:isBold isItalic:isItalic]
                };

                newAttributedText = [[NSAttributedString alloc] initWithString:logContent attributes:attributes];
            }

            // Combine existing and new attributed text
            NSMutableAttributedString *combinedAttributedText = [[NSMutableAttributedString alloc] initWithAttributedString:existingAttributedText];

            // Add line breaks if there's existing content
            if (existingAttributedText.length > 0) {
                [combinedAttributedText appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n\n"]];
            }

            [combinedAttributedText appendAttributedString:newAttributedText];

            // Set the combined attributed text
            logView.attributedText = combinedAttributedText;
        }
    });
}

- (void)updateLogUIWithAccumulatedLogs:(UITextView *)logView {
    [self updateLogUI:logView
           logContent:@""
            textColor:[UIColor blackColor]
      backgroundColor:[UIColor clearColor]
             fontSize:14
               isBold:NO
             isItalic:NO
       attributedText:nil];
}

#pragma mark - Button Management

- (UIScrollView *)setupButtonsInView:(UIView *)parentView
                              target:(id)target
                        buttonTitles:(NSArray<NSString *> *)buttonTitles
                        buttonColors:(NSArray<UIColor *> *)buttonColors
                     buttonSelectors:(NSArray<NSValue *> *)buttonSelectors
                           topMargin:(CGFloat)topMargin
                         heightRatio:(CGFloat)heightRatio
                        heightOffset:(CGFloat)heightOffset {

    if (!parentView || !buttonTitles || !buttonSelectors || buttonTitles.count == 0 || buttonTitles.count != buttonSelectors.count) return nil;

    __block UIScrollView *scrollView = nil;

    // Execute on main thread synchronously if needed
    if ([NSThread isMainThread]) {
        scrollView = [self createButtonScrollViewWithSelectors:parentView
                                                        target:target
                                                  buttonTitles:buttonTitles
                                                  buttonColors:buttonColors
                                               buttonSelectors:buttonSelectors
                                                     topMargin:topMargin
                                                   heightRatio:heightRatio
                                                  heightOffset:heightOffset];
    } else {
        dispatch_sync(dispatch_get_main_queue(), ^{
            scrollView = [self createButtonScrollViewWithSelectors:parentView
                                                            target:target
                                                      buttonTitles:buttonTitles
                                                      buttonColors:buttonColors
                                                   buttonSelectors:buttonSelectors
                                                         topMargin:topMargin
                                                       heightRatio:heightRatio
                                                      heightOffset:heightOffset];
        });
    }

    return scrollView;
}

- (UIScrollView *)setupButtonsInView:(UIView *)parentView
                              target:(id)target
                        buttonTitles:(NSArray<NSString *> *)buttonTitles
                        buttonColors:(NSArray<UIColor *> *)buttonColors
                     buttonSelectors:(NSArray<NSValue *> *)buttonSelectors {
    return [self setupButtonsInView:parentView
                             target:target
                       buttonTitles:buttonTitles
                       buttonColors:buttonColors
                    buttonSelectors:buttonSelectors
                          topMargin:120
                        heightRatio:0.5
                       heightOffset:140];
}

- (UIScrollView *)setupButtonsInViewWithStringSelectors:(UIView *)parentView
                                                 target:(id)target
                                           buttonTitles:(NSArray<NSString *> *)buttonTitles
                                           buttonColors:(NSArray<UIColor *> *)buttonColors
                                          buttonActions:(NSArray<NSString *> *)buttonActions {

    if (!parentView || !buttonTitles || !buttonActions || buttonTitles.count == 0 || buttonTitles.count != buttonActions.count) return nil;

    __block UIScrollView *scrollView = nil;

    // Execute on main thread synchronously if needed
    if ([NSThread isMainThread]) {
        scrollView = [self createButtonScrollView:parentView
                                           target:target
                                     buttonTitles:buttonTitles
                                     buttonColors:buttonColors
                                    buttonActions:buttonActions
                                        topMargin:120
                                      heightRatio:0.5
                                     heightOffset:140];
    } else {
        dispatch_sync(dispatch_get_main_queue(), ^{
            scrollView = [self createButtonScrollView:parentView
                                               target:target
                                         buttonTitles:buttonTitles
                                         buttonColors:buttonColors
                                        buttonActions:buttonActions
                                            topMargin:120
                                          heightRatio:0.5
                                         heightOffset:140];
        });
    }

    return scrollView;
}

- (UIScrollView *)createButtonScrollView:(UIView *)parentView
                                  target:(id)target
                            buttonTitles:(NSArray<NSString *> *)buttonTitles
                            buttonColors:(NSArray<UIColor *> *)buttonColors
                           buttonActions:(NSArray<NSString *> *)buttonActions
                               topMargin:(CGFloat)topMargin
                             heightRatio:(CGFloat)heightRatio
                            heightOffset:(CGFloat)heightOffset {

    // Create a UIScrollView
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    scrollView.backgroundColor = [UIColor clearColor];
    scrollView.layer.borderColor = [UIColor blackColor].CGColor;
    scrollView.layer.borderWidth = 0.5;
    scrollView.layer.cornerRadius = 5;
    [parentView addSubview:scrollView];

    // Set constraints for the scroll view
    CGFloat scrollViewHeight = parentView.frame.size.height * heightRatio - heightOffset;
    [NSLayoutConstraint activateConstraints:@[
        [scrollView.topAnchor constraintEqualToAnchor:parentView.topAnchor constant:topMargin],
        [scrollView.heightAnchor constraintEqualToConstant:scrollViewHeight],
        [scrollView.leadingAnchor constraintEqualToAnchor:parentView.leadingAnchor],
        [scrollView.trailingAnchor constraintEqualToAnchor:parentView.trailingAnchor]
    ]];

    UIButton *previousButton = nil;
    CGFloat buttonHeight = 40;
    CGFloat buttonSpacing = 10;
    CGFloat sideMargin = 5;
    CGFloat currentRowWidth = sideMargin; // Start with left margin
    CGFloat availableWidth = parentView.frame.size.width - (sideMargin * 2); // Account for both side margins

    NSInteger numberOfButtons = buttonTitles.count;
    for (NSInteger i = 0; i < numberOfButtons; i++) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem];
        button.translatesAutoresizingMaskIntoConstraints = NO;

        // Set button color - use provided color or random color
        UIColor *buttonColor;
        if (buttonColors && i < buttonColors.count) {
            buttonColor = buttonColors[i];
        } else {
            buttonColor = [self randomColor];
        }
        button.backgroundColor = buttonColor;

        [button setTitle:buttonTitles[i] forState:UIControlStateNormal];
        [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        button.layer.cornerRadius = 10;

        // Convert string to selector and add target
        SEL action = NSSelectorFromString(buttonActions[i]);
        [button addTarget:target action:action forControlEvents:UIControlEventTouchUpInside];
        [scrollView addSubview:button];

        // Calculate the width of the button based on the title length
        CGFloat buttonWidth = [button intrinsicContentSize].width + 20; // Add some padding
        [NSLayoutConstraint activateConstraints:@[
            [button.widthAnchor constraintEqualToConstant:buttonWidth],
            [button.heightAnchor constraintEqualToConstant:buttonHeight]
        ]];

        if (previousButton) {
            // Check if adding this button would exceed the available width
            CGFloat neededWidth = currentRowWidth + buttonSpacing + buttonWidth;
            BOOL shouldStartNewRow = neededWidth > availableWidth;

            if (shouldStartNewRow) {
                // Start a new row
                [NSLayoutConstraint activateConstraints:@[
                    [button.topAnchor constraintEqualToAnchor:previousButton.bottomAnchor constant:buttonSpacing],
                    [button.leadingAnchor constraintEqualToAnchor:scrollView.leadingAnchor constant:sideMargin]
                ]];
                currentRowWidth = sideMargin + buttonWidth; // Reset row width
            } else {
                // Continue on the same row
                [NSLayoutConstraint activateConstraints:@[
                    [button.topAnchor constraintEqualToAnchor:previousButton.topAnchor],
                    [button.leadingAnchor constraintEqualToAnchor:previousButton.trailingAnchor constant:buttonSpacing]
                ]];
                currentRowWidth += buttonSpacing + buttonWidth;
            }
        } else {
            // First button
            [NSLayoutConstraint activateConstraints:@[
                [button.topAnchor constraintEqualToAnchor:scrollView.topAnchor constant:0],
                [button.leadingAnchor constraintEqualToAnchor:scrollView.leadingAnchor constant:sideMargin]
            ]];
            currentRowWidth = sideMargin + buttonWidth;
        }
        previousButton = button;
    }

    // Set the bottom constraint of the last button to be the bottom of the scroll view
    if (previousButton) {
        [previousButton.bottomAnchor constraintEqualToAnchor:scrollView.bottomAnchor].active = YES;
    }

    return scrollView;
}

- (UIScrollView *)createButtonScrollViewWithSelectors:(UIView *)parentView
                                               target:(id)target
                                         buttonTitles:(NSArray<NSString *> *)buttonTitles
                                         buttonColors:(NSArray<UIColor *> *)buttonColors
                                      buttonSelectors:(NSArray<NSValue *> *)buttonSelectors
                                            topMargin:(CGFloat)topMargin
                                          heightRatio:(CGFloat)heightRatio
                                         heightOffset:(CGFloat)heightOffset {

    // Create a UIScrollView
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    scrollView.backgroundColor = [UIColor clearColor];
    scrollView.layer.borderColor = [UIColor blackColor].CGColor;
    scrollView.layer.borderWidth = 0.5;
    scrollView.layer.cornerRadius = 5;
    [parentView addSubview:scrollView];

    // Set constraints for the scroll view
    CGFloat scrollViewHeight = parentView.frame.size.height * heightRatio - heightOffset;
    [NSLayoutConstraint activateConstraints:@[
        [scrollView.topAnchor constraintEqualToAnchor:parentView.topAnchor constant:topMargin],
        [scrollView.heightAnchor constraintEqualToConstant:scrollViewHeight],
        [scrollView.leadingAnchor constraintEqualToAnchor:parentView.leadingAnchor],
        [scrollView.trailingAnchor constraintEqualToAnchor:parentView.trailingAnchor]
    ]];

    UIButton *previousButton = nil;
    CGFloat buttonHeight = 40;
    CGFloat buttonSpacing = 10;
    CGFloat sideMargin = 5;
    CGFloat currentRowWidth = sideMargin; // Start with left margin
    CGFloat availableWidth = parentView.frame.size.width - (sideMargin * 2); // Account for both side margins

    NSInteger numberOfButtons = buttonTitles.count;
    for (NSInteger i = 0; i < numberOfButtons; i++) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem];
        button.translatesAutoresizingMaskIntoConstraints = NO;

        // Set button color - use provided color or random color
        UIColor *buttonColor;
        if (buttonColors && i < buttonColors.count) {
            buttonColor = buttonColors[i];
        } else {
            buttonColor = [self randomColor];
        }
        button.backgroundColor = buttonColor;

        [button setTitle:buttonTitles[i] forState:UIControlStateNormal];
        [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        button.layer.cornerRadius = 10;

        // Extract selector from NSValue and add target
        SEL action = [buttonSelectors[i] pointerValue];
        [button addTarget:target action:action forControlEvents:UIControlEventTouchUpInside];
        [scrollView addSubview:button];

        // Calculate the width of the button based on the title length
        CGFloat buttonWidth = [button intrinsicContentSize].width + 20; // Add some padding
        [NSLayoutConstraint activateConstraints:@[
            [button.widthAnchor constraintEqualToConstant:buttonWidth],
            [button.heightAnchor constraintEqualToConstant:buttonHeight]
        ]];

        if (previousButton) {
            // Check if adding this button would exceed the available width
            CGFloat neededWidth = currentRowWidth + buttonSpacing + buttonWidth;
            BOOL shouldStartNewRow = neededWidth > availableWidth;

            if (shouldStartNewRow) {
                // Start a new row
                [NSLayoutConstraint activateConstraints:@[
                    [button.topAnchor constraintEqualToAnchor:previousButton.bottomAnchor constant:buttonSpacing],
                    [button.leadingAnchor constraintEqualToAnchor:scrollView.leadingAnchor constant:sideMargin]
                ]];
                currentRowWidth = sideMargin + buttonWidth; // Reset row width
            } else {
                // Continue on the same row
                [NSLayoutConstraint activateConstraints:@[
                    [button.topAnchor constraintEqualToAnchor:previousButton.topAnchor],
                    [button.leadingAnchor constraintEqualToAnchor:previousButton.trailingAnchor constant:buttonSpacing]
                ]];
                currentRowWidth += buttonSpacing + buttonWidth;
            }
        } else {
            // First button
            [NSLayoutConstraint activateConstraints:@[
                [button.topAnchor constraintEqualToAnchor:scrollView.topAnchor constant:0],
                [button.leadingAnchor constraintEqualToAnchor:scrollView.leadingAnchor constant:sideMargin]
            ]];
            currentRowWidth = sideMargin + buttonWidth;
        }
        previousButton = button;
    }

    // Set the bottom constraint of the last button to be the bottom of the scroll view
    if (previousButton) {
        [previousButton.bottomAnchor constraintEqualToAnchor:scrollView.bottomAnchor].active = YES;
    }

    return scrollView;
}

- (UIColor *)randomColor {
    CGFloat red = (CGFloat)(arc4random_uniform(256)) / 255.0;
    CGFloat green = (CGFloat)(arc4random_uniform(256)) / 255.0;
    CGFloat blue = (CGFloat)(arc4random_uniform(256)) / 255.0;
    return [UIColor colorWithRed:red green:green blue:blue alpha:1.0];
}

#pragma mark - VKey Assets Management

- (void)checkVKeyAssetsAndReplace API_AVAILABLE(ios(13.4)) {
    NSArray<NSString *> *fileNames = @[@"firmware", @"signature", @"vkeylicensepack", @"profile"];
    
    NSArray<NSURL *> *documentURLs = [[NSFileManager defaultManager] URLsForDirectory:NSDocumentDirectory
                                                                            inDomains:NSUserDomainMask];
    if (documentURLs.count == 0) {
        [self logError:@"Unable to access documents directory"];
        return;
    }
    
    NSString *documentsPath = documentURLs.firstObject.path;
    
    for (NSString *fileName in fileNames) {
        NSString *docFilePath = [documentsPath stringByAppendingPathComponent:fileName];
        NSString *bundleFilePath = [[NSBundle mainBundle] pathForResource:fileName ofType:nil];
        
        NSString *docSha256 = [self sha256HashOfFileAtPath:docFilePath];
        NSString *bundleSha256 = [self sha256HashOfFileAtPath:bundleFilePath ?: @""];
        
        [self logInfo:[NSString stringWithFormat:@"^Checking file: %@", fileName]];
        [self logDebug:[NSString stringWithFormat:@"^BundleID SHA-256:   %@", bundleSha256 ?: @"nil"]];
        [self logDebug:[NSString stringWithFormat:@"^Document folder SHA-256: %@", docSha256 ?: @"nil"]];
        
        BOOL shouldCopy = NO;
        shouldCopy = (docSha256 && bundleSha256 && ![docSha256 isEqualToString:bundleSha256] && bundleFilePath);
        
        if (shouldCopy) {
            NSError *error = nil;
            
            if ([[NSFileManager defaultManager] fileExistsAtPath:docFilePath]) {
                if (![[NSFileManager defaultManager] removeItemAtPath:docFilePath error:&error]) {
                    [self logError:[NSString stringWithFormat:@"Error removing file %@: %@", fileName, error.localizedDescription]];
                    continue;
                }
            }
            
            if ([[NSFileManager defaultManager] copyItemAtPath:bundleFilePath toPath:docFilePath error:&error]) {
                [self logSuccess:[NSString stringWithFormat:@"Replace: %@", fileName]];
            } else {
                [self logError:[NSString stringWithFormat:@"Error replacing file %@: %@", fileName, error.localizedDescription]];
            }
        }
    }
}

- (NSString *)sha256HashOfFileAtPath:(NSString *)path {
    if (!path || path.length == 0) {
        return nil;
    }
    
    NSFileHandle *fileHandle = [NSFileHandle fileHandleForReadingAtPath:path];
    if (!fileHandle) {
        return nil;
    }
    
    CC_SHA256_CTX context;
    CC_SHA256_Init(&context);
    
    NSData *data;
    do {
        @autoreleasepool {
            data = [fileHandle readDataOfLength:1024 * 1024];
            if (data.length > 0) {
                CC_SHA256_Update(&context, data.bytes, (CC_LONG)data.length);
            }
        }
    } while (data.length > 0);
    
    [fileHandle closeFile];
    
    unsigned char digest[CC_SHA256_DIGEST_LENGTH];
    CC_SHA256_Final(digest, &context);
    
    NSMutableString *hashString = [NSMutableString stringWithCapacity:CC_SHA256_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_SHA256_DIGEST_LENGTH; i++) {
        [hashString appendFormat:@"%02hhx", digest[i]];
    }
    
    return [hashString copy];
}

@end
