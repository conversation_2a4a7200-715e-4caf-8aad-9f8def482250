<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Allow debugger to attach to this process -->
    <key>com.apple.security.get-task-allow</key>
    <true/>
    
    <!-- Optional: Allow debugging of child processes -->
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    
    <!-- Optional: Disable library validation for debugging -->
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    
    <!-- Add your existing entitlements below this line -->
    <!-- Example of other common entitlements: -->
    
    <!-- App Sandbox (if needed) -->
    <!--
    <key>com.apple.security.app-sandbox</key>
    <true/>
    -->
    
    <!-- Network access (if needed) -->
    <!--
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.security.network.server</key>
    <true/>
    -->
    
    <!-- File access (if needed) -->
    <!--
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
    -->
</dict>
</plist>
