import React, { useRef, useEffect, useState, useCallback } from 'react';
import type { PropsWithChildren } from 'react';
import {
  Alert,
  AppState,
  Button,
  DeviceEventEmitter,
  NativeModules,
  Platform,
  Pressable,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  useColorScheme,
  View,
} from 'react-native';

import { Colors } from 'react-native/Libraries/NewAppScreen';
import { launchCamera, launchImageLibrary, ImagePickerResponse, MediaType, CameraOptions, ImageLibraryOptions } from 'react-native-image-picker';
import { check, request, PERMISSIONS, RESULTS, openSettings, PermissionStatus } from 'react-native-permissions';
import { CameraRoll, PhotoIdentifier, GetPhotosParams, SaveToCameraRollOptions } from '@react-native-camera-roll/camera-roll';
import axios, { AxiosResponse, AxiosError } from 'axios';
import TrackPlayer, {
  Capability,
  State,
  Track,
  usePlaybackState,
  useProgress
} from 'react-native-track-player';
import { RNCamera } from 'react-native-camera';
import { Linking } from 'react-native';

// @ts-ignore
import { VGuardPlugin } from 'react-native-vguard';

/* ------------------------- Start Screen ------------------------- */

function StartScreen({ onStart }: { onStart: () => void }) {
  const [memoryHogs, setMemoryHogs] = useState<any[]>([]);
  const [memoryUsageMB, setMemoryUsageMB] = useState(0);
  const [isMemoryConsumed, setIsMemoryConsumed] = useState(false);

  // Memory consumption function for StartScreen
  const consumeMemoryOnStart = useCallback(() => {
    console.log('=== Consuming Memory on StartScreen (Simulating Memory Pressure) ===');

    try {
      const newMemoryHogs: any[] = [];
      const chunkSizeMB = 10; // 10MB chunks
      // On Android, avoid duplicating JS heap allocations; rely on native allocation below
      const numChunks = Platform.OS === 'android' ? 0 : 30;

      for (let i = 0; i < numChunks; i++) {
        if (Platform.OS !== 'android') {
          // iOS previously worked fine with strings
          const largeString = 'A'.repeat(chunkSizeMB * 1024 * 1024);
          newMemoryHogs.push(largeString);
        }
        console.log(`StartScreen: Allocated chunk ${i + 1}/${numChunks} (${chunkSizeMB}MB)`);
      }

      setMemoryHogs(newMemoryHogs);
      // Keep a global reference so memory isn't GC'd when StartScreen unmounts (Android only)
      if (Platform.OS === 'android') {
        (globalThis as any).__MEMORY_HOGS__ = newMemoryHogs;
        // Additionally allocate native memory via plugin to keep native usage high
        try {
          const { MemoryPressure } = NativeModules as any;
          if (MemoryPressure && typeof MemoryPressure.allocate === 'function') {
            // ~300MB in 10MB chunks to be visible in profiler, avoid OOM
            MemoryPressure.allocate(300, 10);
          }
        } catch (e) {
          console.log('allocateNativeMemory failed', e);
        }
      }
      const totalMB = Platform.OS === 'android' ? 300 : numChunks * chunkSizeMB;
      setMemoryUsageMB(totalMB);
      setIsMemoryConsumed(true);

      console.log(`StartScreen: Total memory allocated: ~${totalMB}MB`);
      console.log('StartScreen: Memory pressure simulation active');

      Alert.alert(
        'Memory Pressure Active',
        `StartScreen has allocated ~${totalMB}MB of memory to simulate memory pressure. This will help test app behavior under memory constraints.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log(`StartScreen: Memory allocation failed: ${errorMessage}`);
      Alert.alert('Memory Allocation Failed', `Error: ${errorMessage}`);
    }
  }, []);

  // Trigger memory consumption when component mounts
  useEffect(() => {
    // Delay memory consumption slightly to allow UI to render first
    const timer = setTimeout(() => {
      consumeMemoryOnStart();
    }, 1000); // 1 second delay

    return () => clearTimeout(timer);
  }, [consumeMemoryOnStart]);

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
      <View style={startStyles.container}>
        <Text style={startStyles.title}>Start profiling at this screen and click start to setup VGuard</Text>

        {isMemoryConsumed && (
          <View style={{ marginVertical: 20, padding: 15, backgroundColor: '#ffebee', borderRadius: 8 }}>
            <Text style={{ color: '#c62828', fontWeight: 'bold', textAlign: 'center' }}>
              🧠 Memory Pressure Active
            </Text>
            <Text style={{ color: '#c62828', textAlign: 'center', marginTop: 5 }}>
              ~{memoryUsageMB}MB allocated for testing
            </Text>
          </View>
        )}

        <Pressable
          onPress={onStart}
          style={({ pressed }) => [startStyles.button, pressed && startStyles.buttonPressed]}
          accessibilityRole="button"
          accessibilityLabel="Start"
          testID="start-button"
        >
          <Text style={startStyles.buttonText}>Start</Text>
        </Pressable>

        {isMemoryConsumed && (
          <Text style={{ textAlign: 'center', marginTop: 15, color: '#666', fontSize: 12 }}>
            Memory pressure simulation is active. Monitor app performance and behavior.
          </Text>
        )}
        {isMemoryConsumed && Platform.OS === 'android' && (
          <View style={{ marginTop: 10 }}>
            <Button
              title="Release Android Native Memory"
              onPress={() => {
                try {
                  const { MemoryPressure } = NativeModules as any;
                  if (MemoryPressure && typeof MemoryPressure.free === 'function') {
                    MemoryPressure.free();
                  }
                  (globalThis as any).__MEMORY_HOGS__ = [];
                  setMemoryHogs([]);
                  setIsMemoryConsumed(false);
                  setMemoryUsageMB(0);
                } catch (e) {
                  console.log('freeNativeMemory failed', e);
                }
              }}
              color="#EF5350"
            />
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

/* ----------------------- Home Screen ----------------------- */

type SectionProps = PropsWithChildren<{ title: string }>;

function Section({ children }: SectionProps): JSX.Element {
  return (
    <View style={styles.sectionContainer}>
      <Text style={styles.ButtonStyle}>{children}</Text>
    </View>
  );
}

function App(): JSX.Element {
  const [started, setStarted] = useState(false);

  const [logs, setLogs] = useState('');
  const [logItem, setLogItem] = useState('');
  const [payload, onChangePayload] = useState('message to sign');

  const appState = useRef(AppState.currentState);
  const [, setAppStateVisible] = useState(appState.current);

  const getData = async () => {
    try {
      const url = `https://api.restful-api.dev/objects`;
      const response = await fetch(url);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      const result = await response.json();
      await new Promise(resolve => setTimeout(resolve, 30000));
      return { result };
    } catch (error) {
      console.error('Error fetching data:', error);
      return null;
    }
  };

  const printLogs = (log: string) => {
    setLogItem(log);
    console.log(log);
  };

  const clearLogs = () => {
    setLogs('');
    setLogItem('');
    printLogs('=== Logs Cleared ===');
  };

  useEffect(() => {
    if (!logItem) return;
    setLogs(prev => (prev ? prev + '\n\n' + logItem : logItem));
  }, [logItem]);

  useEffect(() => {
    if (!started) return;

    DeviceEventEmitter.addListener(VGuardPlugin.VGUARD_EVENTS, onVGuardEvents);
    startVGuard();

    return () => {
      DeviceEventEmitter.removeAllListeners(VGuardPlugin.VGUARD_EVENTS);
    };
  }, [started]);

  useEffect(() => {
    if (!started) return;

    const subscription = AppState.addEventListener('change', nextAppState => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        console.log('App has come to the foreground!');
        startVGuard();
      }
      appState.current = nextAppState;
      setAppStateVisible(appState.current);
      console.log('AppState', appState.current);
    });

    return () => subscription.remove();
  }, [started]);

  const startVGuard = () => {
    printLogs('START VGUARD');

    const tlaUrl = 'https://stg-cloud.v-key.com';
    if (tlaUrl) VGuardPlugin.setLoggerBaseUrl(tlaUrl);

    const tiUrl = 'https://stg-cloud.v-key.com/';
    if (tiUrl) VGuardPlugin.setThreatIntelligenceServerURL(tiUrl);

    // 0: DEFAULT, 1: HIGH
    VGuardPlugin.setMemoryConfiguration(1);

    if (Platform.OS === 'android') {
      VGuardPlugin.setOverlayDetectionEnabled(false);
    }

    VGuardPlugin.setupVGuard();
  };

  const onVGuardEvents = async (event: any) => {
    const action = event.action;
    printLogs('. event.action: ' + action);

    if (action == VGuardPlugin.ACTION_SCAN_COMPLETE) {
      const threats = event.data;
      if (threats != null && threats.length > 0) {
        // handle threats if needed
      } else {
        printLogs('Scan complete, no threats found!');
      }
    } else if (action == VGuardPlugin.VOS_READY) {
      printLogs('v-os return code: ' + event.data);
      const errorCode = event.data;
      if (errorCode == '-1039' || errorCode == '20050') {
        // iOS behavior
      }
      const tid = await VGuardPlugin.getTroubleshootingId();
      printLogs('TID: ' + tid);
    } else if (action == VGuardPlugin.VGUARD_ERROR) {
      const errorCode = event.data;
      if (errorCode == '-1039' || errorCode == '20050') {
        showWarningAlert('Emulator is detected: ' + errorCode);
      } else {
        printLogs('Initialize V-Guard is failure caused: ' + errorCode);
      }
    } else if (action == VGuardPlugin.VGUARD_VIRTUAL_SPACE_DETECTED) {
      showWarningAlert('app is running on Virtual Space');
    } else if (action == VGuardPlugin.VGUARD_OVERLAY_DETECTED) {
      showWarningAlert('OVERLAY DETECTED!');
    } else if (action == VGuardPlugin.VGUARD_SCREEN_SHARING_DETECTED) {
      if (Platform.OS === 'android') {
        showWarningAlert('OVERLAY DETECTED!');
      } else if (Platform.OS === 'ios') {
        showWarningAlert('SCREEN_SHARING DETECTED!');
      }
    } else if (action == VGuardPlugin.VGUARD_HANDLE_THREAT_POLICY) {
      const data = event.data;
      const threats = data.threats;
      if (threats == null || threats.length === 0) {
        printLogs('VGUARD_HANDLE_THREAT_POLICY, no threats found!');
      }
    } else {
      printLogs('. event.data: ' + event.data);
    }
  };

  const showWarningAlert = (message: string) => {
    printLogs(message);
    Alert.alert('Warning', message, [{ text: 'OK', onPress: () => '' }]);
  };

  const openCamera = () => {
    Alert.alert(
      'Select Image',
      'Choose an option',
      [
        { text: 'Camera', onPress: () => launchCameraFunction() },
        { text: 'Gallery', onPress: () => launchGalleryFunction() },
        { text: 'Cancel', style: 'cancel' },
      ],
      { cancelable: true }
    );
  };

  const launchCameraFunction = () => {
    const options: CameraOptions = {
      mediaType: 'photo',
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
      quality: 0.8,
      saveToPhotos: true, // This saves the photo to the gallery automatically
    };

    launchCamera(options, (response: ImagePickerResponse) => {
      if (response.didCancel) {
        printLogs('Camera cancelled');
      } else if (response.errorMessage) {
        printLogs('Camera Error: ' + response.errorMessage);
        Alert.alert('Error', response.errorMessage);
      } else if (response.assets && response.assets[0]) {
        const imageUri = response.assets[0].uri;
        printLogs('Photo taken and saved to gallery: ' + imageUri);
        Alert.alert('Success', 'Photo taken and saved to gallery!');
      }
    });
  };

  const launchGalleryFunction = () => {
    const options: ImageLibraryOptions = {
      mediaType: 'photo',
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
      quality: 0.8,
    };

    launchImageLibrary(options, (response: ImagePickerResponse) => {
      if (response.didCancel) {
        printLogs('Gallery cancelled');
      } else if (response.errorMessage) {
        printLogs('Gallery Error: ' + response.errorMessage);
        Alert.alert('Error', response.errorMessage);
      } else if (response.assets && response.assets[0]) {
        const imageUri = response.assets[0].uri;
        printLogs('Photo selected from gallery: ' + imageUri);
        Alert.alert('Success', 'Photo selected from gallery!');
      }
    });
  };

  const checkPermissions = async () => {
    printLogs('=== Checking Permissions ===');

    try {
      const cameraPermission = Platform.OS === 'ios' ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA;
      const photoPermission = Platform.OS === 'ios' ? PERMISSIONS.IOS.PHOTO_LIBRARY : PERMISSIONS.ANDROID.READ_MEDIA_IMAGES;
      const microphonePermission = Platform.OS === 'ios' ? PERMISSIONS.IOS.MICROPHONE : PERMISSIONS.ANDROID.RECORD_AUDIO;

      printLogs('Checking camera permission...');
      const cameraStatus = await check(cameraPermission);
      printLogs(`Camera: ${cameraStatus}`);

      printLogs('Checking photo library permission...');
      const photoStatus = await check(photoPermission);
      printLogs(`Photo Library: ${photoStatus}`);

      printLogs('Checking microphone permission...');
      const microphoneStatus = await check(microphonePermission);
      printLogs(`Microphone: ${microphoneStatus}`);

      const statusText = `Camera: ${cameraStatus}\nPhoto Library: ${photoStatus}\nMicrophone: ${microphoneStatus}`;
      Alert.alert('Permissions Status', statusText);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Error checking permissions: ' + errorMessage);
      Alert.alert('Error', `Failed to check permissions: ${errorMessage}`);
    }
  };

  const requestPermissions = async () => {
    printLogs('=== Requesting Permissions ===');

    try {
      const cameraPermission = Platform.OS === 'ios' ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA;
      const photoPermission = Platform.OS === 'ios' ? PERMISSIONS.IOS.PHOTO_LIBRARY : PERMISSIONS.ANDROID.READ_MEDIA_IMAGES;
      const microphonePermission = Platform.OS === 'ios' ? PERMISSIONS.IOS.MICROPHONE : PERMISSIONS.ANDROID.RECORD_AUDIO;

      printLogs('Requesting camera permission...');
      const cameraResult = await request(cameraPermission);
      printLogs(`Camera request result: ${cameraResult}`);

      printLogs('Requesting photo library permission...');
      const photoResult = await request(photoPermission);
      printLogs(`Photo Library request result: ${photoResult}`);

      printLogs('Requesting microphone permission...');
      const microphoneResult = await request(microphonePermission);
      printLogs(`Microphone request result: ${microphoneResult}`);

      const resultText = `Camera: ${cameraResult}\nPhoto Library: ${photoResult}\nMicrophone: ${microphoneResult}`;
      Alert.alert('Permission Results', resultText);

      // Check if any permission was blocked
      if (cameraResult === RESULTS.BLOCKED || photoResult === RESULTS.BLOCKED || microphoneResult === RESULTS.BLOCKED) {
        Alert.alert(
          'Permissions Required',
          'Some permissions were denied. Please enable them in Settings to use all features.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => openSettings() }
          ]
        );
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Error requesting permissions: ' + errorMessage);
      Alert.alert('Error', `Failed to request permissions: ${errorMessage}`);
    }
  };

  const managePermissions = () => {
    Alert.alert(
      'Permissions Manager',
      'Choose an action:',
      [
        { text: 'Check Status', onPress: checkPermissions },
        { text: 'Request All', onPress: requestPermissions },
        { text: 'Open Settings', onPress: () => openSettings() },
        { text: 'Cancel', style: 'cancel' },
      ],
      { cancelable: true }
    );
  };

  const getPhotosFromCameraRoll = async () => {
    printLogs('=== Getting Photos from Camera Roll ===');

    try {
      // First test if camera roll is available (handles emulators gracefully)
      const isAvailable = await testCameraRollAvailability();
      if (!isAvailable) {
        Alert.alert(
          'Camera Roll Not Available',
          'Camera roll functionality is not working on this device. This is common on emulators or devices without photos. Please try on a real device with photos in the gallery.',
          [
            { text: 'Try Anyway', onPress: () => {
              // Continue with the operation anyway
              printLogs('User chose to try camera roll operation anyway');
            }},
            { text: 'Cancel', style: 'cancel' }
          ]
        );
        // Don't return here - let user try anyway if they want
      }

      // For Android, try READ_MEDIA_IMAGES first (Android 13+), then fallback to READ external storage
      let permissions: string[] = [];

      if (Platform.OS === 'ios') {
        permissions = [PERMISSIONS.IOS.PHOTO_LIBRARY];
      } else {
        // Android: Try modern permission first
        permissions = [PERMISSIONS.ANDROID.READ_MEDIA_IMAGES];
      }

      printLogs(`Checking permissions: ${permissions.join(', ')}`);

      let hasPermission = false;

      for (const permission of permissions) {
        const permissionStatus = await check(permission);
        printLogs(`Permission ${permission}: ${permissionStatus}`);

        if (permissionStatus === RESULTS.GRANTED) {
          hasPermission = true;
          break;
        } else if (permissionStatus === RESULTS.DENIED) {
          const requestResult = await request(permission);
          printLogs(`Permission request result for ${permission}: ${requestResult}`);

          if (requestResult === RESULTS.GRANTED) {
            hasPermission = true;
            break;
          }
        }
      }

      // If modern Android permission failed, try legacy permission
      if (!hasPermission && Platform.OS === 'android') {
        printLogs('Trying legacy READ_EXTERNAL_STORAGE permission...');
        const legacyPermission = PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;
        const legacyStatus = await check(legacyPermission);
        printLogs(`Legacy permission ${legacyPermission}: ${legacyStatus}`);

        if (legacyStatus === RESULTS.GRANTED) {
          hasPermission = true;
        } else if (legacyStatus === RESULTS.DENIED) {
          const legacyResult = await request(legacyPermission);
          printLogs(`Legacy permission request result: ${legacyResult}`);
          if (legacyResult === RESULTS.GRANTED) {
            hasPermission = true;
          }
        }
      }

      if (!hasPermission) {
        Alert.alert(
          'Permission Required',
          'Camera roll read access is required. Please enable "Photos and videos" permission in settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => openSettings() }
          ]
        );
        return;
      }

      const params: GetPhotosParams = {
        first: 10, // Get first 10 photos
        assetType: 'Photos', // Only photos, not videos
        groupTypes: 'All',
      };

      printLogs('Fetching photos from camera roll...');

      // Try with a more basic approach for Android with better error handling
      let result;
      try {
        if (Platform.OS === 'android') {
          // For Android, try with minimal parameters first
          try {
            result = await CameraRoll.getPhotos({
              first: 5,
              assetType: 'Photos',
            });
          } catch (androidError) {
            printLogs(`Android specific error: ${androidError}`);
            // Try with even more basic parameters
            result = await CameraRoll.getPhotos({
              first: 1,
            });
          }
        } else {
          result = await CameraRoll.getPhotos(params);
        }

        printLogs(`Found ${result.edges.length} photos`);
      } catch (nativeError) {
        // Handle native module errors (common on emulators)
        const errorStr = String(nativeError);
        printLogs(`Native camera roll error: ${errorStr}`);

        if (errorStr.includes('CameraRollModule') || errorStr.includes('native call')) {
          Alert.alert(
            'Camera Roll Error',
            'Camera roll functionality is not working properly on this device/emulator. This is common on Android emulators without photos.',
            [{ text: 'OK' }]
          );
          return;
        }

        // Re-throw if it's not a known emulator issue
        throw nativeError;
      }

      if (result.edges.length > 0) {
        const photoUris = result.edges.map((edge, index) =>
          `${index + 1}. ${edge.node.image.uri}`
        ).join('\n');

        printLogs('Photo URIs:\n' + photoUris);
        Alert.alert(
          'Camera Roll Photos',
          `Found ${result.edges.length} photos:\n\n${photoUris.substring(0, 200)}${photoUris.length > 200 ? '...' : ''}`,
          [{ text: 'OK' }]
        );
      } else {
        printLogs('No photos found in camera roll');
        Alert.alert('Camera Roll', 'No photos found in camera roll');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Error getting photos from camera roll: ' + errorMessage);
      Alert.alert('Error', `Failed to get photos: ${errorMessage}`);
    }
  };

  const saveImageToCameraRoll = async () => {
    printLogs('=== Saving Test Image to Camera Roll ===');

    try {
      // Check permissions first - simplified approach
      let hasWritePermission = false;

      if (Platform.OS === 'ios') {
        const permission = PERMISSIONS.IOS.PHOTO_LIBRARY_ADD_ONLY;
        const status = await check(permission);
        printLogs(`iOS write permission ${permission}: ${status}`);

        if (status === RESULTS.GRANTED) {
          hasWritePermission = true;
        } else if (status === RESULTS.DENIED) {
          const result = await request(permission);
          hasWritePermission = result === RESULTS.GRANTED;
        }
      } else {
        // Android - try READ_MEDIA_IMAGES first (for saving we need read access too)
        const readPermission = PERMISSIONS.ANDROID.READ_MEDIA_IMAGES;
        const readStatus = await check(readPermission);
        printLogs(`Android read permission ${readPermission}: ${readStatus}`);

        if (readStatus === RESULTS.GRANTED) {
          hasWritePermission = true;
        } else if (readStatus === RESULTS.DENIED) {
          const readResult = await request(readPermission);
          hasWritePermission = readResult === RESULTS.GRANTED;
        }
      }

      if (!hasWritePermission) {
        Alert.alert(
          'Permission Required',
          'Camera roll write access is required. Please enable "Photos and videos" permission in settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => openSettings() }
          ]
        );
        return;
      }

      // Use a sample image URL with proper extension for MIME type detection
      const imageUrl = 'https://picsum.photos/400/400.jpg';

      printLogs('Saving image to camera roll: ' + imageUrl);

      // First test if camera roll is available (handles emulators gracefully)
      const isAvailable = await testCameraRollAvailability();
      if (!isAvailable) {
        Alert.alert(
          'Camera Roll Not Available',
          'Camera roll functionality is not working on this device. This is common on emulators. Please try on a real device.',
          [
            { text: 'Try Anyway', onPress: () => {
              printLogs('User chose to try camera roll save operation anyway');
            }},
            { text: 'Cancel', style: 'cancel' }
          ]
        );
        // Don't return here - let user try anyway if they want
      }

      // For Android, we need to handle MIME type properly by downloading first
      if (Platform.OS === 'android') {
        printLogs('Android detected - downloading image first...');

        try {
          // Download the image to get proper blob with MIME type
          const response = await fetch(imageUrl);
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const blob = await response.blob();
          printLogs(`Downloaded image - Type: ${blob.type}, Size: ${blob.size} bytes`);

          // Convert blob to base64 data URI with proper MIME type
          const reader = new FileReader();
          const base64Promise = new Promise<string>((resolve, reject) => {
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
          });
          reader.readAsDataURL(blob);

          const base64DataUri = await base64Promise;
          printLogs('Converted to base64 data URI');

          // Save the base64 data URI which includes proper MIME type
          const result = await CameraRoll.saveAsset(base64DataUri, {
            type: 'photo',
            album: 'TestVersion0753',
          });

          printLogs('Image saved successfully: ' + result);
          Alert.alert('Success', `Image saved to camera roll!\nPath: ${result}`);
        } catch (downloadError) {
          printLogs('Download failed, trying direct URL save...');
          // Fallback to direct URL save
          const result = await CameraRoll.saveAsset(imageUrl, {
            type: 'photo',
            album: 'TestVersion0753',
          });

          printLogs('Image saved successfully (fallback): ' + result);
          Alert.alert('Success', `Image saved to camera roll!\nPath: ${result}`);
        }
      } else {
        // iOS can handle the URL directly
        const options: SaveToCameraRollOptions = {
          type: 'photo',
          album: 'TestVersion0753', // Custom album name
        };

        const result = await CameraRoll.saveAsset(imageUrl, options);

        printLogs('Image saved successfully: ' + result);
        Alert.alert('Success', `Image saved to camera roll!\nPath: ${result}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Error saving image to camera roll: ' + errorMessage);
      Alert.alert('Error', `Failed to save image: ${errorMessage}`);
    }
  };

  // Helper function to test camera roll availability with proper error handling
  const testCameraRollAvailability = async (): Promise<boolean> => {
    try {
      printLogs('Testing camera roll availability...');

      // Try the most basic camera roll operation
      const result = await CameraRoll.getPhotos({
        first: 1,
      });

      printLogs(`Camera roll test successful: found ${result.edges.length} items`);
      return true;
    } catch (error) {
      const errorStr = String(error);
      printLogs(`Camera roll test failed: ${errorStr}`);

      // Check if it's a known emulator/native module issue
      if (errorStr.includes('CameraRollModule') ||
          errorStr.includes('native call') ||
          errorStr.includes('No photos') ||
          errorStr.includes('Permission denied')) {
        return false;
      }

      // For other errors, assume it might work with proper permissions
      return false;
    }
  };

  const manageCameraRoll = async () => {
    printLogs('=== Managing Camera Roll ===');

    // Test if camera roll is available first
    const isAvailable = await testCameraRollAvailability();
    if (!isAvailable) {
      Alert.alert(
        'Camera Roll Unavailable',
        'Camera roll functionality is not working on this device. This is common on emulators. You can still check permissions or try the operations.',
        [
          { text: 'Check Permissions', onPress: managePermissions },
          { text: 'Try Operations Anyway', onPress: () => {
            // Show the normal camera roll options
            Alert.alert(
              'Camera Roll Manager',
              'Choose an action:',
              [
                { text: 'Get Photos', onPress: getPhotosFromCameraRoll },
                { text: 'Save Test Image', onPress: saveImageToCameraRoll },
                { text: 'Cancel', style: 'cancel' },
              ],
              { cancelable: true }
            );
          }},
          { text: 'Cancel', style: 'cancel' },
        ]
      );
      return;
    }

    Alert.alert(
      'Camera Roll Manager',
      'Choose an action:',
      [
        { text: 'Get Photos', onPress: getPhotosFromCameraRoll },
        { text: 'Save Test Image', onPress: saveImageToCameraRoll },
        { text: 'Cancel', style: 'cancel' },
      ],
      { cancelable: true }
    );
  };

  const makeGetRequest = async () => {
    printLogs('=== Making GET Request ===');

    try {
      const url = 'https://jsonplaceholder.typicode.com/posts/1';
      printLogs('Making GET request to: ' + url);

      const response: AxiosResponse = await axios.get(url, {
        timeout: 10000, // 10 seconds timeout
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'TestVersion0753-ReactNative',
        },
      });

      printLogs(`GET Response Status: ${response.status}`);
      printLogs(`GET Response Data: ${JSON.stringify(response.data, null, 2)}`);

      Alert.alert(
        'GET Request Success',
        `Status: ${response.status}\n\nTitle: ${response.data.title}\n\nBody: ${response.data.body.substring(0, 100)}...`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = axiosError.response
        ? `HTTP ${axiosError.response.status}: ${axiosError.response.statusText}`
        : axiosError.message || String(error);

      printLogs('GET Request Error: ' + errorMessage);
      Alert.alert('GET Request Failed', `Error: ${errorMessage}`);
    }
  };

  const makePostRequest = async () => {
    printLogs('=== Making POST Request ===');

    try {
      const url = 'https://jsonplaceholder.typicode.com/posts';
      const postData = {
        title: 'Test Post from TestVersion0753',
        body: 'This is a test post created from React Native app using axios',
        userId: 1,
        timestamp: new Date().toISOString(),
      };

      printLogs('Making POST request to: ' + url);
      printLogs('POST Data: ' + JSON.stringify(postData, null, 2));

      const response: AxiosResponse = await axios.post(url, postData, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'TestVersion0753-ReactNative',
        },
      });

      printLogs(`POST Response Status: ${response.status}`);
      printLogs(`POST Response Data: ${JSON.stringify(response.data, null, 2)}`);

      Alert.alert(
        'POST Request Success',
        `Status: ${response.status}\n\nCreated ID: ${response.data.id}\n\nTitle: ${response.data.title}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = axiosError.response
        ? `HTTP ${axiosError.response.status}: ${axiosError.response.statusText}`
        : axiosError.message || String(error);

      printLogs('POST Request Error: ' + errorMessage);
      Alert.alert('POST Request Failed', `Error: ${errorMessage}`);
    }
  };

  const makeMultipleRequests = async () => {
    printLogs('=== Making Multiple Concurrent Requests ===');

    try {
      const urls = [
        'https://jsonplaceholder.typicode.com/posts/1',
        'https://jsonplaceholder.typicode.com/posts/2',
        'https://jsonplaceholder.typicode.com/posts/3',
      ];

      printLogs('Making concurrent requests to multiple endpoints...');

      const promises = urls.map((url, index) =>
        axios.get(url, {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'TestVersion0753-ReactNative',
          },
        }).then(response => ({
          index: index + 1,
          status: response.status,
          title: response.data.title,
        }))
      );

      const results = await Promise.all(promises);

      printLogs(`All requests completed successfully`);
      results.forEach(result => {
        printLogs(`Post ${result.index}: ${result.title}`);
      });

      const resultText = results.map(r => `${r.index}. ${r.title}`).join('\n\n');

      Alert.alert(
        'Multiple Requests Success',
        `Successfully fetched ${results.length} posts:\n\n${resultText}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = axiosError.response
        ? `HTTP ${axiosError.response.status}: ${axiosError.response.statusText}`
        : axiosError.message || String(error);

      printLogs('Multiple Requests Error: ' + errorMessage);
      Alert.alert('Multiple Requests Failed', `Error: ${errorMessage}`);
    }
  };

  const manageNetworkRequests = () => {
    Alert.alert(
      'Network Requests Manager',
      'Choose a request type:',
      [
        { text: 'GET Request', onPress: makeGetRequest },
        { text: 'POST Request', onPress: makePostRequest },
        { text: 'Multiple Requests', onPress: makeMultipleRequests },
        { text: 'Cancel', style: 'cancel' },
      ],
      { cancelable: true }
    );
  };

  // Audio Player State
  const [isPlayerReady, setIsPlayerReady] = useState(false);
  const [currentTrack, setCurrentTrack] = useState<Track | null>(null);
  const [autoPlayInitialized, setAutoPlayInitialized] = useState(false);
  const playbackState = usePlaybackState();
  const progress = useProgress();

  // Auto-start background audio when app launches
  useEffect(() => {
    if (started && !autoPlayInitialized) {
      // Delay auto-initialization to ensure app is fully loaded
      const timer = setTimeout(() => {
        autoInitializeAudio();
      }, 2000); // 2 second delay

      return () => clearTimeout(timer);
    }
  }, [started, autoPlayInitialized]);

  // Auto-initialize and start background audio
  const autoInitializeAudio = async () => {
    if (autoPlayInitialized) return;

    printLogs('=== Auto-initializing Background Audio ===');

    try {
      // Setup Track Player
      await TrackPlayer.setupPlayer();

      await TrackPlayer.updateOptions({
        capabilities: [
          Capability.Play,
          Capability.Pause,
          Capability.SkipToNext,
          Capability.SkipToPrevious,
          Capability.Stop,
        ],
        compactCapabilities: [
          Capability.Play,
          Capability.Pause,
        ],
      });

      // Add sample tracks
      const tracks: Track[] = [
        {
          id: '1',
          url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
          title: 'SoundHelix Song 1',
          artist: 'SoundHelix',
          duration: 60,
        },
        {
          id: '2',
          url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3',
          title: 'SoundHelix Song 2',
          artist: 'SoundHelix',
          duration: 60,
        },
        {
          id: '3',
          url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3',
          title: 'SoundHelix Song 3',
          artist: 'SoundHelix',
          duration: 60,
        },
      ];

      await TrackPlayer.add(tracks);
      setCurrentTrack(tracks[0]);

      // Start playing automatically
      await TrackPlayer.play();

      setIsPlayerReady(true);
      setAutoPlayInitialized(true);

      printLogs('Background audio auto-initialized and started playing');
      printLogs(`Added ${tracks.length} tracks and started playback`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Auto-initialize audio error: ' + errorMessage);
      console.error('Failed to auto-initialize background audio:', errorMessage);
    }
  };

  // Initialize Track Player (manual)
  const setupPlayer = async () => {
    printLogs('=== Setting up Track Player ===');

    try {
      await TrackPlayer.setupPlayer();

      await TrackPlayer.updateOptions({
        capabilities: [
          Capability.Play,
          Capability.Pause,
          Capability.SkipToNext,
          Capability.SkipToPrevious,
          Capability.Stop,
        ],
        compactCapabilities: [
          Capability.Play,
          Capability.Pause,
        ],
      });

      setIsPlayerReady(true);
      printLogs('Track Player setup completed successfully');
      Alert.alert('Audio Player', 'Track Player initialized successfully!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Track Player setup error: ' + errorMessage);
      Alert.alert('Error', `Failed to setup Track Player: ${errorMessage}`);
    }
  };

  const addSampleTracks = async () => {
    printLogs('=== Adding Sample Tracks ===');

    try {
      const tracks: Track[] = [
        {
          id: '1',
          url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
          title: 'SoundHelix Song 1',
          artist: 'SoundHelix',
          duration: 60,
        },
        {
          id: '2',
          url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3',
          title: 'SoundHelix Song 2',
          artist: 'SoundHelix',
          duration: 60,
        },
        {
          id: '3',
          url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3',
          title: 'SoundHelix Song 3',
          artist: 'SoundHelix',
          duration: 60,
        },
      ];

      await TrackPlayer.add(tracks);
      setCurrentTrack(tracks[0]);

      printLogs(`Added ${tracks.length} tracks to queue`);
      Alert.alert('Success', `Added ${tracks.length} sample tracks to the queue!`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Add tracks error: ' + errorMessage);
      Alert.alert('Error', `Failed to add tracks: ${errorMessage}`);
    }
  };

  const playAudio = async () => {
    printLogs('=== Playing Audio ===');

    try {
      await TrackPlayer.play();
      printLogs('Audio playback started');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Play audio error: ' + errorMessage);
      Alert.alert('Error', `Failed to play audio: ${errorMessage}`);
    }
  };

  const pauseAudio = async () => {
    printLogs('=== Pausing Audio ===');

    try {
      await TrackPlayer.pause();
      printLogs('Audio playback paused');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Pause audio error: ' + errorMessage);
      Alert.alert('Error', `Failed to pause audio: ${errorMessage}`);
    }
  };

  const stopAudio = async () => {
    printLogs('=== Stopping Audio ===');

    try {
      await TrackPlayer.stop();
      printLogs('Audio playback stopped');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Stop audio error: ' + errorMessage);
      Alert.alert('Error', `Failed to stop audio: ${errorMessage}`);
    }
  };

  const skipToNext = async () => {
    printLogs('=== Skipping to Next Track ===');

    try {
      await TrackPlayer.skipToNext();
      const track = await TrackPlayer.getActiveTrack();
      setCurrentTrack(track || null);
      printLogs(`Skipped to next track: ${track?.title || 'Unknown'}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Skip to next error: ' + errorMessage);
      Alert.alert('Error', `Failed to skip to next: ${errorMessage}`);
    }
  };

  const skipToPrevious = async () => {
    printLogs('=== Skipping to Previous Track ===');

    try {
      await TrackPlayer.skipToPrevious();
      const track = await TrackPlayer.getActiveTrack();
      setCurrentTrack(track || null);
      printLogs(`Skipped to previous track: ${track?.title || 'Unknown'}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Skip to previous error: ' + errorMessage);
      Alert.alert('Error', `Failed to skip to previous: ${errorMessage}`);
    }
  };

  const getPlayerStatus = async () => {
    printLogs('=== Getting Player Status ===');

    try {
      const state = await TrackPlayer.getPlaybackState();
      const track = await TrackPlayer.getActiveTrack();
      const position = await TrackPlayer.getPosition();
      const duration = await TrackPlayer.getDuration();

      const stateText = state.state === State.Playing ? 'Playing' :
                       state.state === State.Paused ? 'Paused' :
                       state.state === State.Stopped ? 'Stopped' : 'Unknown';

      printLogs(`Player State: ${stateText}`);
      printLogs(`Current Track: ${track?.title || 'None'}`);
      printLogs(`Position: ${Math.floor(position)}s / ${Math.floor(duration)}s`);

      Alert.alert(
        'Player Status',
        `State: ${stateText}\n\nTrack: ${track?.title || 'None'}\nArtist: ${track?.artist || 'Unknown'}\n\nPosition: ${Math.floor(position)}s / ${Math.floor(duration)}s`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('Get player status error: ' + errorMessage);
      Alert.alert('Error', `Failed to get player status: ${errorMessage}`);
    }
  };

  const manageAudioPlayer = () => {
    Alert.alert(
      'Background Audio Player',
      'Choose an action:',
      [
        { text: 'Setup Player', onPress: setupPlayer },
        { text: 'Add Tracks', onPress: addSampleTracks },
        { text: 'Play', onPress: playAudio },
        { text: 'Pause', onPress: pauseAudio },
        { text: 'Stop', onPress: stopAudio },
        { text: 'Next Track', onPress: skipToNext },
        { text: 'Previous Track', onPress: skipToPrevious },
        { text: 'Player Status', onPress: getPlayerStatus },
        { text: 'Cancel', style: 'cancel' },
      ],
      { cancelable: true }
    );
  };

  // QR Code Scanner State
  const [isQRScannerVisible, setIsQRScannerVisible] = useState(false);
  const [scannedData, setScannedData] = useState<string | null>(null);

  // Start QR code scanning with permission check
  const startQRScanning = async () => {
    printLogs('=== Starting QR Code Scanning ===');

    try {
      // Check camera permission first
      const cameraPermission = await check(PERMISSIONS.IOS.CAMERA);
      printLogs(`Camera permission status: ${cameraPermission}`);

      if (cameraPermission === RESULTS.GRANTED) {
        setScannedData(null);
        setIsQRScannerVisible(true);
        printLogs('QR Scanner opened');
      } else if (cameraPermission === RESULTS.DENIED) {
        // Request permission
        const requestResult = await request(PERMISSIONS.IOS.CAMERA);
        printLogs(`Camera permission request result: ${requestResult}`);

        if (requestResult === RESULTS.GRANTED) {
          setScannedData(null);
          setIsQRScannerVisible(true);
          printLogs('QR Scanner opened after permission granted');
        } else {
          Alert.alert(
            'Camera Permission Required',
            'Please enable camera access in Settings to use QR code scanner.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Open Settings', onPress: () => Linking.openSettings() },
            ]
          );
        }
      } else {
        Alert.alert(
          'Camera Permission Required',
          'Please enable camera access in Settings to use QR code scanner.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => Linking.openSettings() },
          ]
        );
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      printLogs('QR Scanner permission error: ' + errorMessage);
      Alert.alert('Error', `Failed to check camera permission: ${errorMessage}`);
    }
  };

  // Stop QR code scanning
  const stopQRScanning = () => {
    printLogs('=== Stopping QR Code Scanning ===');
    setIsQRScannerVisible(false);
    printLogs('QR Scanner closed');
  };

  // Handle QR code scan result
  const onQRCodeScanned = (e: any) => {
    const data = e.data || 'Unknown data';
    const type = e.type || 'Unknown';

    printLogs('=== QR Code Scanned ===');
    printLogs(`Scanned data: ${data}`);
    printLogs(`Code type: ${type}`);

    setScannedData(data);
    setIsQRScannerVisible(false);

    Alert.alert(
      'QR Code Scanned!',
      `Data: ${data}\n\nType: ${type}`,
      [
        { text: 'Scan Another', onPress: startQRScanning },
        { text: 'OK', style: 'default' },
      ]
    );
  };



  // Show QR scanner info
  const showQRScannerInfo = () => {
    const lastScanned = scannedData ? `Last scanned: ${scannedData}` : 'No QR codes scanned yet';

    Alert.alert(
      'QR Code Scanner Info',
      `Status: Ready\n\n${lastScanned}`,
      [{ text: 'OK' }]
    );
  };

  // Manage QR code scanner
  const manageQRScanner = () => {
    Alert.alert(
      'QR Code Scanner',
      'Choose an action:',
      [
        { text: 'Start Scanning', onPress: startQRScanning },
        { text: 'Scanner Info', onPress: showQRScannerInfo },
        { text: 'Cancel', style: 'cancel' },
      ],
      { cancelable: true }
    );
  };

  const isDarkMode = useColorScheme() === 'dark';

  if (!started) {
    return <StartScreen onStart={() => setStarted(true)} />;
  }

  const SafeAreaViewStyle = { backgroundColor: Colors.white };
  const scrollViewStyle = { height: 300, backgroundColor: 'lightgray', borderRadius: 5, margin: 10 };
  const logStyle = { color: 'black' };

  return (
    <SafeAreaView style={SafeAreaViewStyle}>
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      <View style={styles.sectionContainer}>
        <TextInput
          style={styles.input}
          placeholder="Type here to sign!"
          onChangeText={onChangePayload}
          value={payload}
        />
      </View>

      <Section title="">
        <Button
          onPress={() => {
            printLogs('trigger request scan');
            VGuardPlugin.requestScan();
          }}
          title="scan"
          color="#841584"
        />
        <View style={{ padding: 2 }} />

        <Button
          onPress={() => {
            printLogs('trigger forceSyncLogs');
            VGuardPlugin.forceSyncLogs();
          }}
          title="forceSyncLogs"
          color="#841584"
        />
        <View style={{ padding: 2 }} />

        <Button onPress={() => VGuardPlugin.showPopupVkey('', '', true, 5)} title="show popup quit 1s" color="#841584" />
        <View style={{ padding: 2 }} />

        <Button onPress={() => VGuardPlugin.showPopupVkey('', '', false, 5)} title="show popup no quit 1s" color="#841584" />
        <View style={{ padding: 2 }} />

        <Button onPress={openCamera} title="Camera" color="#2196F3" />
        <View style={{ padding: 2 }} />

        <Button onPress={managePermissions} title="Permissions" color="#FF9800" />
        <View style={{ padding: 2 }} />

        <Button onPress={manageCameraRoll} title="Camera Roll" color="#4CAF50" />
        <View style={{ padding: 2 }} />

        <Button onPress={manageNetworkRequests} title="Network Requests" color="#9C27B0" />
        <View style={{ padding: 2 }} />

        <Button onPress={manageAudioPlayer} title="Background Audio" color="#FF5722" />
        <View style={{ padding: 2 }} />

        <Button onPress={manageQRScanner} title="QR Code Scanner" color="#795548" />
      </Section>

      <Section title="Logs" />
      <View style={styles.sectionContainer}>
        <Button onPress={clearLogs} title="Clear Logs" color="#FF6B6B" />
        <View style={{ padding: 2 }} />
      </View>
      <ScrollView style={scrollViewStyle}>
        <View style={styles.sectionContainer}>
          <Text style={logStyle}> {logs} </Text>
        </View>
      </ScrollView>

      {/* QR Code Scanner Modal */}
      {isQRScannerVisible && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'black',
          zIndex: 1000,
        }}>
          {/* Top instruction text */}
          <View style={{
            position: 'absolute',
            top: 60,
            left: 0,
            right: 0,
            zIndex: 1001,
            paddingHorizontal: 20,
          }}>
            <Text style={{
              fontSize: 18,
              color: 'white',
              textAlign: 'center',
              backgroundColor: 'rgba(0,0,0,0.5)',
              padding: 10,
              borderRadius: 5,
            }}>
              Point camera at QR code to scan
            </Text>
          </View>

          {/* Camera */}
          <RNCamera
            style={{
              flex: 1,
            }}
            type={RNCamera.Constants.Type.back}
            flashMode={RNCamera.Constants.FlashMode.off}
            captureAudio={false}
            androidCameraPermissionOptions={{
              title: 'Permission to use camera',
              message: 'We need your permission to use your camera',
              buttonPositive: 'Ok',
              buttonNegative: 'Cancel',
            }}
            onBarCodeRead={onQRCodeScanned}
            barCodeTypes={[RNCamera.Constants.BarCodeType.qr]}
          />

          {/* Scanning overlay */}
          <View style={{
            position: 'absolute',
            top: '30%',
            left: '20%',
            right: '20%',
            bottom: '30%',
            borderWidth: 2,
            borderColor: '#00ff00',
            backgroundColor: 'transparent',
            zIndex: 1001,
          }} />

          {/* Bottom close button */}
          <View style={{
            position: 'absolute',
            bottom: 60,
            left: 0,
            right: 0,
            zIndex: 1001,
            paddingHorizontal: 20,
          }}>
            <Button
              onPress={stopQRScanning}
              title="Close Scanner"
              color="#f44336"
            />
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

/* ----------------------------- Styles ---------------------------- */

const styles = StyleSheet.create({
  sectionContainer: {
    marginTop: 10,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '600',
  },
  ButtonStyle: {
    backgroundColor: Colors.lighter,
  },
  highlight: {
    fontWeight: '700',
  },
  input: {
    height: 40,
    width: 330,
    margin: 12,
    borderWidth: 1,
    padding: 10,
    backgroundColor: Colors.lighter,
    color: Colors.black,
  },
});

const startStyles = StyleSheet.create({
  container: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 24 },
  title: { fontSize: 22, fontWeight: '700', marginBottom: 16, color: '#111827' },
  button: {
    minWidth: 160,
    paddingVertical: 14,
    paddingHorizontal: 20,
    backgroundColor: '#4f46e5',
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonPressed: { opacity: 0.9, transform: [{ scale: 0.98 }] },
  buttonText: { color: 'white', fontSize: 16, fontWeight: '700', letterSpacing: 0.3 },
});

export default App;
