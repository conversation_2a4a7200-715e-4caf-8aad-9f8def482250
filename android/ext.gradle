ext {
    asset_environment = 'release'
    proj_vkeygen_version = '4.5.+'

    proj_signature_specifier = 'vos-app-protection-signature'
    proj_signature_version = 'latest.integration'

    proj_firmware_version = '4.10.1.1'
    proj_firmware_specifier = 'vos-firmware'
    proj_firmware_classifier = ''
    proj_firmware_pqr_classifier = 'pqr'

    proj_processor_android_version = '4.10.4.1'
    proj_processor_android_specifier = 'vos-processor-android'

    proj_vguard_android_version = '4.10.2.12'
    proj_vguard_android_specifier = 'vos-app-protection-android'

    proj_smarttoken_android_version = '4.10.3.0'
    proj_smarttoken_android_specifier = 'vos-smarttoken-android'

    proj_otp_android_version = '4.10.0.0'
    proj_otp_android_specifier = 'otp-android'

    versionFile = new File(project.rootDir, 'version.properties')
}
allprojects {
    if (project.hasProperty("asset_environment") && project.findProperty("asset_environment") != '') {
        asset_environment = project.findProperty("asset_environment")
    }
    if (project.hasProperty("vkeygen_version") && project.findProperty("vkeygen_version") != '') {
        proj_vkeygen_version = project.findProperty("vkeygen_version")
    }
    if (project.hasProperty("signature_specifier") && project.findProperty("signature_specifier") != '') {
        proj_signature_specifier = project.findProperty("signature_specifier")
    }
    if (project.hasProperty("signature_version")) {
        proj_signature_version = project.findProperty('signature_version')
    }

    if (project.hasProperty("firmware_version")) {
        proj_firmware_version = project.findProperty('firmware_version')
    }
    if (project.hasProperty("firmware_specifier") && project.findProperty("firmware_specifier") != '') {
        proj_firmware_specifier = project.findProperty("firmware_specifier")
    }

    if (project.hasProperty("firmware_classifier") && project.findProperty("firmware_classifier") != '') {
        proj_firmware_classifier = project.findProperty('firmware_classifier')
        proj_firmware_pqr_classifier = "$proj_firmware_classifier-pqr"
    }

    if (project.hasProperty("processor_android_version")) {
        proj_processor_android_version = project.findProperty('processor_android_version')
    }
    if (project.hasProperty("vos_specifier_version") && project.findProperty("vos_specifier_version") != '') {
        proj_processor_android_specifier = project.findProperty("vos_specifier_version")
    }
    if (project.hasProperty("vguard_android_version")) {
        proj_vguard_android_version = project.findProperty('vguard_android_version')
    }
    if (project.hasProperty("vguard_specifier_version") && project.findProperty("vguard_specifier_version") != '') {
        proj_vguard_android_specifier = project.findProperty("vguard_specifier_version")
    }

    if (project.hasProperty("smarttoken_android_version")) {
        proj_smarttoken_android_version = project.findProperty('smarttoken_android_version')
    }
    if (project.hasProperty("smarttoken_specifier_version") && project.findProperty("smarttoken_specifier_version") != '') {
        proj_smarttoken_android_specifier = project.findProperty("smarttoken_specifier_version")
    }

    if (project.hasProperty("otp_android_version")) {
        proj_otp_android_version = project.findProperty('otp_android_version')
    }
    if (project.hasProperty("otp_specifier_version") && project.findProperty("otp_specifier_version") != '') {
        proj_otp_android_specifier = project.findProperty("otp_specifier_version")
    }
}