apply from: 'ext.gradle'
buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

allprojects {
    repositories {
        jcenter()

        google()
        mavenLocal()
        flatDir {
            dirs 'libs'
        }
        maven {
            switch ("${asset_environment}") {
                case "release":
                    url = "${artifactory_contextUrl}/libs-release"
                    break
                case "staging":
                    url = "${artifactory_contextUrl}/libs-qa-release-local"
                    break
                case "develop":
                default:
                    url = "${artifactory_contextUrl}/libs-snapshot"
                    break
            }
            credentials {
                username "${artifactory_user}"
                password "${artifactory_password}"
            }
            setAllowInsecureProtocol(true)
        }

        maven {
            url 'https://maven.google.com/'
            name 'Google'
        }

        maven { url 'https://developer.huawei.com/repo/' }
    }
}


task clean(type: Delete) {
    delete rootProject.buildDir
}

apply plugin: "com.facebook.react.rootproject"
