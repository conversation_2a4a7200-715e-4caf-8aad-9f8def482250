#!/bin/bash -ex
get_script_dir() {
    SOURCE="${BASH_SOURCE[0]}"
    while [ -h "$SOURCE" ]; do # resolve $SOURCE until the file is no longer a symlink
        DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null && pwd )"
        SOURCE="$(readlink "$SOURCE")"
        [[ $SOURCE != /* ]] && SOURCE="$DIR/$SOURCE" # if $SOURCE was a relative symlink, we need to resolve it relative to the path where the symlink file was located
    done
    DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null && pwd )"
    ( >&2 echo "Source DIR: ${DIR}")
    echo "${DIR}"
}

cwd=`get_script_dir`

# Find the latest matching assets zip and extract into the android directory
zip_glob_1="${cwd}/Company_${1}_*.zip"
zip_file=$(ls -1t ${zip_glob_1} 2>/dev/null | head -n1)
if [ -z "$zip_file" ]; then
    echo "ERROR: Assets zip not found matching ${zip_glob}" >&2
    exit 1
fi
unzip -o "$zip_file" -d "$cwd"

echo 'Copy Generated-Assets to app/asset folder....'
mkdir -p "$cwd/app/src/main/assets/"
base_dir=$(ls -dt ${cwd}/Company_${1}_*/ 2>/dev/null | head -n1)
if [ -z "$base_dir" ]; then
    echo "ERROR: Extracted directory not found for Company_${1}_*" >&2
    exit 2
fi
cp -rf "$base_dir/firmware"                                  "$cwd/app/src/main/assets/firmware"
cp -rf "$base_dir/signature_android.enc"                     "$cwd/app/src/main/assets/signature"
cp -rf "$base_dir/header_android"                            "$cwd/app/src/main/assets/header_android"
cp -rf "$base_dir/vkeylicensepack_TestVTap_Android_Customer_Info.txt" "$cwd/app/src/main/assets/vkeylicensepack"
cp -rf "$base_dir/voscodesign.vky"                           "$cwd/app/src/main/assets/voscodesign.vky"
cp -rf "$base_dir/kernel.bin"                                "$cwd/app/src/main/assets/kernel.bin"
echo 'Done Copy Generated-Assets to app/asset folder....'
