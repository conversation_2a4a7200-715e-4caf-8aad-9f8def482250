# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:
# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:
-keep class vkey.** { *; }
-keep class com.vkey.** { *; }
-keep interface vkey.** { *; }
-keep interface com.vkey.** { *; }

-keep class androidx.core.app.VKJobIntentService { *; }
-keep class androidx.core.app.JobIntentService { *; }

# For io.jsonwebtoken
-keepattributes InnerClasses

-keep class io.jsonwebtoken.** { *; }
-keepnames class io.jsonwebtoken.* { *; }
-keepnames interface io.jsonwebtoken.* { *; }

-keep class org.bouncycastle.** { *; }
-keepnames class org.bouncycastle.** { *; }
-dontwarn org.bouncycastle.**

# For cuckoofilter4j
-keep class com.github.mgunlogson.cuckoofilter4j.** { *; }
-keepnames class com.github.mgunlogson.cuckoofilter4j.* { *; }
-keepnames interface com.github.mgunlogson.cuckoofilter4j.* { *; }

# Keep safety
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**
-dontwarn vkey.android.pki.pkiTAInterface