package com.testversion0753

import android.os.Build
import android.os.MemoryFile
import android.os.SharedMemory
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import java.nio.ByteBuffer
import kotlin.math.min

class MemoryPressureModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

  private val pageSize: Int = 4096
  private var sharedMemories: MutableList<SharedMemory> = mutableListOf()
  private var sharedBuffers: MutableList<ByteBuffer> = mutableListOf()
  private var memoryFiles: MutableList<MemoryFile> = mutableListOf()

  override fun getName(): String = "MemoryPressure"

  @ReactMethod
  fun allocate(totalMB: Int, chunkMB: Int, promise: Promise) {
    try {
      freeInternal()
      val totalBytes: Long = totalMB.toLong() * 1024L * 1024L
      val chunkBytes: Int = (chunkMB * 1024 * 1024)
      var allocated: Long = 0

      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
        while (allocated < totalBytes) {
          val thisChunk: Long = min(chunkBytes.toLong(), totalBytes - allocated)
          val sm = SharedMemory.create("MemoryPressure", thisChunk.toInt())
          val buf = sm.mapReadWrite()
          var offset = 0
          while (offset < thisChunk) {
            buf.put(offset, 1)
            offset += pageSize
          }
          sharedMemories.add(sm)
          sharedBuffers.add(buf)
          allocated += thisChunk
        }
      } else {
        val mfChunk: Int = min(chunkBytes, 1 * 1024 * 1024)
        val touchBuffer = ByteArray(1) { 1 }
        while (allocated < totalBytes) {
          val thisChunk: Int = min(mfChunk.toLong(), totalBytes - allocated).toInt()
          val mf = MemoryFile("MemoryPressure", thisChunk)
          var offset = 0
          while (offset < thisChunk) {
            mf.writeBytes(touchBuffer, 0, offset, 1)
            offset += pageSize
          }
          memoryFiles.add(mf)
          allocated += thisChunk
        }
      }

      promise.resolve(allocated / (1024.0 * 1024.0))
    } catch (t: Throwable) {
      promise.reject("ALLOC_FAIL", t)
    }
  }

  @ReactMethod
  fun free(promise: Promise) {
    try {
      val freed = freeInternal()
      promise.resolve(freed)
    } catch (t: Throwable) {
      promise.reject("FREE_FAIL", t)
    }
  }

  private fun freeInternal(): Int {
    var freedCount = 0
    for (buf in sharedBuffers) {
      try { SharedMemory.unmap(buf); freedCount++ } catch (_: Throwable) {}
    }
    sharedBuffers.clear()
    for (sm in sharedMemories) {
      try { sm.close() } catch (_: Throwable) {}
    }
    sharedMemories.clear()

    for (mf in memoryFiles) {
      try { mf.close(); freedCount++ } catch (_: Throwable) {}
    }
    memoryFiles.clear()

    System.gc()
    return freedCount
  }
}
